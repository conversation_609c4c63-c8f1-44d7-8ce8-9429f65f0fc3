<?php
/**
 * Category Model
 * Handles category data operations
 */

class Category {
    private $db;
    private $table = 'categories';
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Create a new category
     */
    public function create($data) {
        $sql = "INSERT INTO {$this->table} (name, slug, description, parent_id, image, icon, sort_order, is_active, created_at, updated_at) 
                VALUES (:name, :slug, :description, :parent_id, :image, :icon, :sort_order, :is_active, NOW(), NOW())";
        
        $params = [
            'name' => $data['name'],
            'slug' => $data['slug'],
            'description' => $data['description'] ?? '',
            'parent_id' => $data['parent_id'],
            'image' => $data['image'] ?? '',
            'icon' => $data['icon'] ?? '',
            'sort_order' => $data['sort_order'] ?? 0,
            'is_active' => $data['is_active'] ?? true
        ];
        
        $this->db->query($sql, $params);
        return $this->db->getConnection()->lastInsertId();
    }
    
    /**
     * Find category by ID
     */
    public function findById($id) {
        $sql = "SELECT c.*, pc.name as parent_name
                FROM {$this->table} c
                LEFT JOIN {$this->table} pc ON c.parent_id = pc.id
                WHERE c.id = :id";
        
        return $this->db->fetch($sql, ['id' => $id]);
    }
    
    /**
     * Find category by slug
     */
    public function findBySlug($slug) {
        $sql = "SELECT c.*, pc.name as parent_name
                FROM {$this->table} c
                LEFT JOIN {$this->table} pc ON c.parent_id = pc.id
                WHERE c.slug = :slug";
        
        return $this->db->fetch($sql, ['slug' => $slug]);
    }
    
    /**
     * Get all categories
     */
    public function getAll($parentId = null, $includeProducts = false) {
        $whereClause = [];
        $params = [];
        
        if ($parentId !== null) {
            if ($parentId === 'root') {
                $whereClause[] = "c.parent_id IS NULL";
            } else {
                $whereClause[] = "c.parent_id = :parent_id";
                $params['parent_id'] = $parentId;
            }
        }
        
        $whereClause[] = "c.is_active = 1";
        $whereSQL = 'WHERE ' . implode(' AND ', $whereClause);
        
        $productCountSQL = $includeProducts ? 
            ", (SELECT COUNT(*) FROM products WHERE category_id = c.id AND status = 'active') as product_count" : "";
        
        $sql = "SELECT c.*, pc.name as parent_name{$productCountSQL}
                FROM {$this->table} c
                LEFT JOIN {$this->table} pc ON c.parent_id = pc.id
                {$whereSQL}
                ORDER BY c.sort_order ASC, c.name ASC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get subcategories
     */
    public function getSubcategories($parentId) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE parent_id = :parent_id AND is_active = 1
                ORDER BY sort_order ASC, name ASC";
        
        return $this->db->fetchAll($sql, ['parent_id' => $parentId]);
    }
    
    /**
     * Get category tree (hierarchical structure)
     */
    public function getTree() {
        // Get all categories
        $sql = "SELECT * FROM {$this->table} WHERE is_active = 1 ORDER BY sort_order ASC, name ASC";
        $categories = $this->db->fetchAll($sql);
        
        // Build tree structure
        return $this->buildTree($categories);
    }
    
    /**
     * Build hierarchical tree from flat array
     */
    private function buildTree($categories, $parentId = null) {
        $tree = [];
        
        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $category['children'] = $this->buildTree($categories, $category['id']);
                $tree[] = $category;
            }
        }
        
        return $tree;
    }
    
    /**
     * Update category
     */
    public function update($id, $data) {
        $setClause = [];
        $params = ['id' => $id];
        
        foreach ($data as $key => $value) {
            if ($key !== 'id') {
                $setClause[] = "{$key} = :{$key}";
                $params[$key] = $value;
            }
        }
        
        $params['updated_at'] = date('Y-m-d H:i:s');
        $setClause[] = "updated_at = :updated_at";
        
        $sql = "UPDATE {$this->table} SET " . implode(', ', $setClause) . " WHERE id = :id";
        return $this->db->query($sql, $params);
    }
    
    /**
     * Delete category
     */
    public function delete($id) {
        $sql = "DELETE FROM {$this->table} WHERE id = :id";
        return $this->db->query($sql, ['id' => $id]);
    }
    
    /**
     * Get product count for category
     */
    public function getProductCount($categoryId) {
        $sql = "SELECT COUNT(*) as count FROM products WHERE category_id = :category_id";
        $result = $this->db->fetch($sql, ['category_id' => $categoryId]);
        return $result['count'];
    }
    
    /**
     * Get popular categories (by product count)
     */
    public function getPopular($limit = 10) {
        $sql = "SELECT c.*, COUNT(p.id) as product_count
                FROM {$this->table} c
                LEFT JOIN products p ON c.id = p.category_id AND p.status = 'active'
                WHERE c.is_active = 1
                GROUP BY c.id
                ORDER BY product_count DESC, c.name ASC
                LIMIT :limit";
        
        return $this->db->fetchAll($sql, ['limit' => $limit]);
    }
    
    /**
     * Search categories
     */
    public function search($query, $limit = 20) {
        $sql = "SELECT * FROM {$this->table}
                WHERE is_active = 1 AND (
                    name LIKE :query OR 
                    description LIKE :query
                )
                ORDER BY 
                    CASE WHEN name LIKE :exact_query THEN 1 ELSE 2 END,
                    name ASC
                LIMIT :limit";
        
        $searchQuery = '%' . $query . '%';
        $exactQuery = '%' . $query . '%';
        
        return $this->db->fetchAll($sql, [
            'query' => $searchQuery,
            'exact_query' => $exactQuery,
            'limit' => $limit
        ]);
    }
    
    /**
     * Check if slug exists
     */
    public function slugExists($slug, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE slug = :slug";
        $params = ['slug' => $slug];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] > 0;
    }
    
    /**
     * Generate unique slug
     */
    public function generateSlug($name, $excludeId = null) {
        $baseSlug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $name)));
        $baseSlug = trim($baseSlug, '-');
        
        $slug = $baseSlug;
        $counter = 1;
        
        while ($this->slugExists($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    /**
     * Get category statistics
     */
    public function getStatistics() {
        $sql = "SELECT 
                    COUNT(*) as total_categories,
                    SUM(CASE WHEN parent_id IS NULL THEN 1 ELSE 0 END) as root_categories,
                    SUM(CASE WHEN parent_id IS NOT NULL THEN 1 ELSE 0 END) as subcategories,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_categories
                FROM {$this->table}";
        
        return $this->db->fetch($sql);
    }
    
    /**
     * Get breadcrumb for category
     */
    public function getBreadcrumb($categoryId) {
        $breadcrumb = [];
        $currentId = $categoryId;
        
        while ($currentId) {
            $category = $this->findById($currentId);
            if (!$category) break;
            
            array_unshift($breadcrumb, [
                'id' => $category['id'],
                'name' => $category['name'],
                'slug' => $category['slug']
            ]);
            
            $currentId = $category['parent_id'];
        }
        
        return $breadcrumb;
    }
}
?>

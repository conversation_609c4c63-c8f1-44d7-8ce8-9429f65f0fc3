<?php
/**
 * Main Entry Point
 * Musanze District Product Price Comparison Platform
 */

// Include configuration
require_once 'backend/config/config.php';

// Simple routing
$request_uri = $_SERVER['REQUEST_URI'];
$script_name = $_SERVER['SCRIPT_NAME'];
$base_path = dirname($script_name);

// Remove base path from request URI
if ($base_path !== '/') {
    $request_uri = substr($request_uri, strlen($base_path));
}

// Remove query string
$request_uri = strtok($request_uri, '?');

// Remove leading slash
$request_uri = ltrim($request_uri, '/');

// Default route
if (empty($request_uri)) {
    $request_uri = 'home';
}

// Route to appropriate page
switch ($request_uri) {
    case 'home':
    case '':
        if (file_exists('frontend/customer/index.html')) {
            include 'frontend/customer/index.html';
        } else {
            showSimpleHomepage();
        }
        break;

    case 'products':
        if (file_exists('frontend/customer/products.html')) {
            include 'frontend/customer/products.html';
        } else {
            showPlaceholderPage('Products', 'Product listing will be available here.');
        }
        break;

    case 'product':
        if (file_exists('frontend/customer/product-detail.html')) {
            include 'frontend/customer/product-detail.html';
        } else {
            showPlaceholderPage('Product Details', 'Product details will be available here.');
        }
        break;

    case 'compare':
        showPlaceholderPage('Price Comparison', 'Price comparison feature will be available here.');
        break;

    case 'cart':
        showPlaceholderPage('Shopping Cart', 'Shopping cart will be available here.');
        break;

    case 'checkout':
        showPlaceholderPage('Checkout', 'Checkout process will be available here.');
        break;

    case 'login':
        if (file_exists('frontend/customer/login.html')) {
            include 'frontend/customer/login.html';
        } else {
            showPlaceholderPage('Login', 'Login page will be available here.');
        }
        break;

    case 'register':
        if (file_exists('frontend/customer/register.html')) {
            include 'frontend/customer/register.html';
        } else {
            showPlaceholderPage('Register', 'Registration page will be available here.');
        }
        break;

    case 'vendor':
        if (file_exists('frontend/vendor/dashboard.html')) {
            include 'frontend/vendor/dashboard.html';
        } else {
            showPlaceholderPage('Vendor Dashboard', 'Vendor dashboard will be available here.');
        }
        break;

    case 'vendor/products':
        showPlaceholderPage('Vendor Products', 'Vendor product management will be available here.');
        break;

    case 'vendor/orders':
        showPlaceholderPage('Vendor Orders', 'Vendor order management will be available here.');
        break;

    case 'admin':
        showPlaceholderPage('Admin Dashboard', 'Admin dashboard will be available here.');
        break;

    default:
        // Check if it's an API request
        if (strpos($request_uri, 'api/') === 0) {
            include 'backend/api/index.php';
        } else {
            // 404 page
            http_response_code(404);
            if (file_exists('frontend/shared/404.html')) {
                include 'frontend/shared/404.html';
            } else {
                show404Page();
            }
        }
        break;
}

// Helper functions for fallback pages
function showSimpleHomepage() {
    echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Musanze Marketplace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="text-center">
            <h1 class="display-4 text-primary mb-4">Musanze Marketplace</h1>
            <p class="lead">Product Price Comparison and Ordering Platform</p>
            <div class="row mt-5">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-database fa-3x text-primary mb-3"></i>
                            <h5>Setup Database</h5>
                            <p>Initialize the database schema</p>
                            <a href="/database/setup.php" class="btn btn-primary">Setup Database</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-code fa-3x text-success mb-3"></i>
                            <h5>API Endpoints</h5>
                            <p>Test the REST API</p>
                            <a href="/api" class="btn btn-success">View API</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-3x text-info mb-3"></i>
                            <h5>User Registration</h5>
                            <p>Create customer or vendor account</p>
                            <a href="/register" class="btn btn-info">Register</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>';
}

function showPlaceholderPage($title, $description) {
    echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . $title . ' - Musanze Marketplace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container">
            <a class="navbar-brand" href="/">Musanze Marketplace</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Home</a>
                <a class="nav-link" href="/login">Login</a>
                <a class="nav-link" href="/register">Register</a>
            </div>
        </div>
    </nav>
    <div class="container mt-5">
        <div class="text-center">
            <h1>' . $title . '</h1>
            <p class="lead">' . $description . '</p>
            <a href="/" class="btn btn-primary">Go Home</a>
        </div>
    </div>
</body>
</html>';
}

function show404Page() {
    echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="text-center">
            <i class="fas fa-exclamation-triangle fa-5x text-warning mb-4"></i>
            <h1 class="display-1">404</h1>
            <h2>Page Not Found</h2>
            <p class="lead">The page you are looking for does not exist.</p>
            <a href="/" class="btn btn-primary">Go Home</a>
        </div>
    </div>
</body>
</html>';
}
?>

<?php
/**
 * Main Entry Point
 * Musanze District Product Price Comparison Platform
 */

// Include configuration
require_once 'backend/config/config.php';

// Simple routing
$request_uri = $_SERVER['REQUEST_URI'];
$script_name = $_SERVER['SCRIPT_NAME'];
$base_path = dirname($script_name);

// Remove base path from request URI
if ($base_path !== '/') {
    $request_uri = substr($request_uri, strlen($base_path));
}

// Remove query string
$request_uri = strtok($request_uri, '?');

// Remove leading slash
$request_uri = ltrim($request_uri, '/');

// Default route
if (empty($request_uri)) {
    $request_uri = 'home';
}

// Route to appropriate page
switch ($request_uri) {
    case 'home':
    case '':
        include 'frontend/customer/index.html';
        break;
        
    case 'products':
        include 'frontend/customer/products.html';
        break;
        
    case 'product':
        include 'frontend/customer/product-detail.html';
        break;
        
    case 'compare':
        include 'frontend/customer/compare.html';
        break;
        
    case 'cart':
        include 'frontend/customer/cart.html';
        break;
        
    case 'checkout':
        include 'frontend/customer/checkout.html';
        break;
        
    case 'login':
        include 'frontend/customer/login.html';
        break;
        
    case 'register':
        include 'frontend/customer/register.html';
        break;
        
    case 'vendor':
        include 'frontend/vendor/dashboard.html';
        break;
        
    case 'vendor/products':
        include 'frontend/vendor/products.html';
        break;
        
    case 'vendor/orders':
        include 'frontend/vendor/orders.html';
        break;
        
    case 'admin':
        include 'frontend/admin/dashboard.html';
        break;
        
    default:
        // Check if it's an API request
        if (strpos($request_uri, 'api/') === 0) {
            include 'backend/api/index.php';
        } else {
            // 404 page
            http_response_code(404);
            include 'frontend/shared/404.html';
        }
        break;
}
?>

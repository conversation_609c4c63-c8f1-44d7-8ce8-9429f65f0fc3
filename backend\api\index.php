<?php
/**
 * API Router
 * Musanze District Product Price Comparison Platform
 */

// Include configuration
require_once '../config/config.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get request method and URI
$method = $_SERVER['REQUEST_METHOD'];
$request_uri = $_SERVER['REQUEST_URI'];

// Remove base path and query string
$base_path = dirname(dirname($_SERVER['SCRIPT_NAME']));
if ($base_path !== '/') {
    $request_uri = substr($request_uri, strlen($base_path));
}
$request_uri = strtok($request_uri, '?');

// Remove /api prefix
$request_uri = preg_replace('#^/api/?#', '', $request_uri);

// Split URI into parts
$uri_parts = array_filter(explode('/', $request_uri));

// API response helper
function sendResponse($data, $status = 200, $message = 'Success') {
    http_response_code($status);
    echo json_encode([
        'success' => $status < 400,
        'status' => $status,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('c')
    ]);
    exit();
}

// Error handler
function sendError($message, $status = 400, $errors = null) {
    http_response_code($status);
    echo json_encode([
        'success' => false,
        'status' => $status,
        'message' => $message,
        'errors' => $errors,
        'timestamp' => date('c')
    ]);
    exit();
}

// Get request body
$input = json_decode(file_get_contents('php://input'), true);

try {
    // Route requests
    if (empty($uri_parts)) {
        sendResponse([
            'name' => 'Musanze Marketplace API',
            'version' => '1.0.0',
            'endpoints' => [
                'auth' => '/api/auth',
                'products' => '/api/products',
                'categories' => '/api/categories',
                'vendors' => '/api/vendors',
                'orders' => '/api/orders',
                'users' => '/api/users'
            ]
        ]);
    }
    
    $endpoint = $uri_parts[0];
    
    switch ($endpoint) {
        case 'auth':
            require_once 'auth.php';
            break;
            
        case 'products':
            require_once 'products.php';
            break;
            
        case 'categories':
            require_once 'categories.php';
            break;
            
        case 'vendors':
            require_once 'vendors.php';
            break;
            
        case 'orders':
            require_once 'orders.php';
            break;
            
        case 'users':
            require_once 'users.php';
            break;
            
        default:
            sendError('Endpoint not found', 404);
    }
    
} catch (Exception $e) {
    error_log('API Error: ' . $e->getMessage());
    sendError('Internal server error', 500);
}
?>

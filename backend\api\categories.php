<?php
/**
 * Categories API Endpoints
 */

require_once '../models/Category.php';

// Get remaining URI parts
$categoryId = isset($uri_parts[1]) ? $uri_parts[1] : null;
$action = isset($uri_parts[2]) ? $uri_parts[2] : null;

switch ($method) {
    case 'GET':
        if ($categoryId && $action === 'products') {
            handleGetCategoryProducts($categoryId);
        } elseif ($categoryId) {
            handleGetCategory($categoryId);
        } else {
            handleGetCategories();
        }
        break;
        
    case 'POST':
        requireAuth(['admin']);
        handleCreateCategory();
        break;
        
    case 'PUT':
        requireAuth(['admin']);
        if (!$categoryId) {
            sendError('Category ID is required', 400);
        }
        handleUpdateCategory($categoryId);
        break;
        
    case 'DELETE':
        requireAuth(['admin']);
        if (!$categoryId) {
            sendError('Category ID is required', 400);
        }
        handleDeleteCategory($categoryId);
        break;
        
    default:
        sendError('Method not allowed', 405);
}

function handleGetCategories() {
    try {
        $categoryModel = new Category();
        
        $includeProducts = isset($_GET['include_products']) && $_GET['include_products'] === 'true';
        $parentId = $_GET['parent_id'] ?? null;
        
        $categories = $categoryModel->getAll($parentId, $includeProducts);
        
        sendResponse(['categories' => $categories]);
        
    } catch (Exception $e) {
        error_log('Get categories error: ' . $e->getMessage());
        sendError('Failed to get categories', 500);
    }
}

function handleGetCategory($categoryId) {
    try {
        $categoryModel = new Category();
        $category = $categoryModel->findById($categoryId);
        
        if (!$category) {
            sendError('Category not found', 404);
        }
        
        // Get subcategories
        $subcategories = $categoryModel->getSubcategories($categoryId);
        $category['subcategories'] = $subcategories;
        
        sendResponse(['category' => $category]);
        
    } catch (Exception $e) {
        error_log('Get category error: ' . $e->getMessage());
        sendError('Failed to get category', 500);
    }
}

function handleGetCategoryProducts($categoryId) {
    try {
        require_once '../models/Product.php';
        
        $productModel = new Product();
        
        // Get query parameters
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 20;
        
        $filters = [
            'category_id' => $categoryId,
            'search' => $_GET['q'] ?? null,
            'min_price' => $_GET['min_price'] ?? null,
            'max_price' => $_GET['max_price'] ?? null,
            'vendor_id' => $_GET['vendor'] ?? null,
            'status' => 'active'
        ];
        
        $sort = $_GET['sort'] ?? 'created_at';
        $order = $_GET['order'] ?? 'desc';
        
        $result = $productModel->getAll($page, $limit, $filters, $sort, $order);
        
        sendResponse($result);
        
    } catch (Exception $e) {
        error_log('Get category products error: ' . $e->getMessage());
        sendError('Failed to get category products', 500);
    }
}

function handleCreateCategory() {
    global $input;
    
    // Validate input
    $required = ['name', 'slug'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            sendError("Field '{$field}' is required", 400);
        }
    }
    
    try {
        $categoryModel = new Category();
        
        $categoryData = [
            'name' => sanitize($input['name']),
            'slug' => sanitize($input['slug']),
            'description' => sanitize($input['description'] ?? ''),
            'parent_id' => $input['parent_id'] ?? null,
            'image' => sanitize($input['image'] ?? ''),
            'icon' => sanitize($input['icon'] ?? ''),
            'sort_order' => $input['sort_order'] ?? 0,
            'is_active' => $input['is_active'] ?? true
        ];
        
        $categoryId = $categoryModel->create($categoryData);
        
        // Get created category
        $category = $categoryModel->findById($categoryId);
        
        sendResponse([
            'category' => $category,
            'message' => 'Category created successfully'
        ], 201);
        
    } catch (Exception $e) {
        error_log('Create category error: ' . $e->getMessage());
        sendError('Failed to create category', 500);
    }
}

function handleUpdateCategory($categoryId) {
    global $input;
    
    try {
        $categoryModel = new Category();
        $category = $categoryModel->findById($categoryId);
        
        if (!$category) {
            sendError('Category not found', 404);
        }
        
        $updateData = [];
        $allowedFields = ['name', 'slug', 'description', 'parent_id', 'image', 'icon', 'sort_order', 'is_active'];
        
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                if (in_array($field, ['name', 'slug', 'description', 'image', 'icon'])) {
                    $updateData[$field] = sanitize($input[$field]);
                } else {
                    $updateData[$field] = $input[$field];
                }
            }
        }
        
        $categoryModel->update($categoryId, $updateData);
        
        // Get updated category
        $updatedCategory = $categoryModel->findById($categoryId);
        
        sendResponse([
            'category' => $updatedCategory,
            'message' => 'Category updated successfully'
        ]);
        
    } catch (Exception $e) {
        error_log('Update category error: ' . $e->getMessage());
        sendError('Failed to update category', 500);
    }
}

function handleDeleteCategory($categoryId) {
    try {
        $categoryModel = new Category();
        $category = $categoryModel->findById($categoryId);
        
        if (!$category) {
            sendError('Category not found', 404);
        }
        
        // Check if category has products
        $productCount = $categoryModel->getProductCount($categoryId);
        if ($productCount > 0) {
            sendError('Cannot delete category with existing products', 400);
        }
        
        // Check if category has subcategories
        $subcategories = $categoryModel->getSubcategories($categoryId);
        if (!empty($subcategories)) {
            sendError('Cannot delete category with subcategories', 400);
        }
        
        $categoryModel->delete($categoryId);
        
        sendResponse(['message' => 'Category deleted successfully']);
        
    } catch (Exception $e) {
        error_log('Delete category error: ' . $e->getMessage());
        sendError('Failed to delete category', 500);
    }
}
?>

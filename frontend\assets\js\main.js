/**
 * Main JavaScript
 * Musanze Marketplace
 */

// Global variables
let currentUser = null;
let isAuthenticated = false;

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * Initialize the application
 */
async function initializeApp() {
    try {
        // Initialize authentication
        await initializeAuth();
        
        // Initialize UI components
        initializeUI();
        
        // Initialize search functionality
        initializeSearch();
        
        // Initialize cart
        initializeCart();
        
        // Initialize language
        initializeLanguage();
        
        // Initialize back to top button
        initializeBackToTop();
        
        console.log('Musanze Marketplace initialized successfully');
    } catch (error) {
        console.error('Error initializing application:', error);
    }
}

/**
 * Initialize authentication
 */
async function initializeAuth() {
    try {
        // Check if user is already authenticated
        const response = await api.getCurrentUser();
        if (response.success) {
            currentUser = response.data.user;
            isAuthenticated = true;
            updateAuthUI();
        }
    } catch (error) {
        // User not authenticated, which is fine
        currentUser = null;
        isAuthenticated = false;
        updateAuthUI();
    }
}

/**
 * Update authentication UI
 */
function updateAuthUI() {
    const loggedInMenu = document.getElementById('loggedInMenu');
    const loggedOutMenu = document.getElementById('loggedOutMenu');
    const userName = document.getElementById('userName');
    const vendorDashboardLink = document.getElementById('vendorDashboardLink');
    const adminDashboardLink = document.getElementById('adminDashboardLink');
    
    if (isAuthenticated && currentUser) {
        // Show logged in menu
        if (loggedInMenu) loggedInMenu.style.display = 'block';
        if (loggedOutMenu) loggedOutMenu.style.display = 'none';
        if (userName) userName.textContent = currentUser.name;
        
        // Show role-specific links
        if (vendorDashboardLink && ['vendor', 'admin'].includes(currentUser.role)) {
            vendorDashboardLink.style.display = 'block';
        }
        if (adminDashboardLink && currentUser.role === 'admin') {
            adminDashboardLink.style.display = 'block';
        }
    } else {
        // Show logged out menu
        if (loggedInMenu) loggedInMenu.style.display = 'none';
        if (loggedOutMenu) loggedOutMenu.style.display = 'block';
        if (userName) userName.textContent = 'Account';
        
        // Hide role-specific links
        if (vendorDashboardLink) vendorDashboardLink.style.display = 'none';
        if (adminDashboardLink) adminDashboardLink.style.display = 'none';
    }
}

/**
 * Initialize UI components
 */
function initializeUI() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Auto-hide alerts
    const alerts = document.querySelectorAll('.alert[data-auto-hide]');
    alerts.forEach(alert => {
        const delay = parseInt(alert.dataset.autoHide) || 5000;
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => alert.remove(), 300);
        }, delay);
    });
}

/**
 * Initialize search functionality
 */
function initializeSearch() {
    const searchInputs = document.querySelectorAll('#searchInput, #mobileSearchInput, #heroSearchInput');
    const searchButtons = document.querySelectorAll('#searchBtn, #mobileSearchBtn, #heroSearchBtn');
    
    // Add event listeners to search inputs
    searchInputs.forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch(this.value);
            }
        });
    });
    
    // Add event listeners to search buttons
    searchButtons.forEach(button => {
        button.addEventListener('click', function() {
            const input = this.previousElementSibling || 
                         this.parentElement.querySelector('input') ||
                         document.querySelector('#searchInput, #mobileSearchInput, #heroSearchInput');
            if (input) {
                performSearch(input.value);
            }
        });
    });
}

/**
 * Perform search
 */
function performSearch(query) {
    if (!query.trim()) return;
    
    // Save to recent searches
    saveRecentSearch(query);
    
    // Redirect to products page with search query
    window.location.href = `/products?q=${encodeURIComponent(query)}`;
}

/**
 * Save recent search
 */
function saveRecentSearch(query) {
    const recentSearches = storage.get(STORAGE_KEYS.recentSearches, []);
    
    // Remove if already exists
    const index = recentSearches.indexOf(query);
    if (index > -1) {
        recentSearches.splice(index, 1);
    }
    
    // Add to beginning
    recentSearches.unshift(query);
    
    // Keep only last 10 searches
    if (recentSearches.length > 10) {
        recentSearches.splice(10);
    }
    
    storage.set(STORAGE_KEYS.recentSearches, recentSearches);
}

/**
 * Initialize cart functionality
 */
function initializeCart() {
    updateCartCount();
    
    // Listen for cart updates
    document.addEventListener(EVENTS.cartUpdate, updateCartCount);
}

/**
 * Update cart count in navigation
 */
function updateCartCount() {
    const cart = storage.get(STORAGE_KEYS.cart, []);
    const cartCount = cart.reduce((total, item) => total + (item.quantity || 1), 0);
    
    const cartCountElement = document.getElementById('cartCount');
    if (cartCountElement) {
        cartCountElement.textContent = cartCount;
        cartCountElement.style.display = cartCount > 0 ? 'inline' : 'none';
    }
}

/**
 * Initialize language functionality
 */
function initializeLanguage() {
    const currentLanguage = storage.get(STORAGE_KEYS.language, APP_CONFIG.defaultLanguage);
    setLanguage(currentLanguage);
}

/**
 * Change language
 */
function changeLanguage(language) {
    if (APP_CONFIG.languages[language]) {
        storage.set(STORAGE_KEYS.language, language);
        setLanguage(language);
        
        // Dispatch language change event
        document.dispatchEvent(new CustomEvent(EVENTS.languageChange, {
            detail: { language }
        }));
        
        showToast(`Language changed to ${APP_CONFIG.languages[language]}`, 'success');
    }
}

/**
 * Set language
 */
function setLanguage(language) {
    const languageDropdown = document.getElementById('languageDropdown');
    if (languageDropdown) {
        const languageText = language.toUpperCase();
        languageDropdown.innerHTML = `<i class="fas fa-globe me-1"></i> ${languageText}`;
    }
    
    // Set document language
    document.documentElement.lang = language;
}

/**
 * Initialize back to top button
 */
function initializeBackToTop() {
    const backToTopButton = document.getElementById('backToTop');
    if (!backToTopButton) return;
    
    // Show/hide button based on scroll position
    window.addEventListener('scroll', throttle(() => {
        if (window.pageYOffset > 300) {
            backToTopButton.style.display = 'flex';
        } else {
            backToTopButton.style.display = 'none';
        }
    }, 100));
    
    // Scroll to top when clicked
    backToTopButton.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

/**
 * Global logout function
 */
async function logout() {
    try {
        await api.logout();
        currentUser = null;
        isAuthenticated = false;
        updateAuthUI();
        
        // Clear user-specific data
        storage.remove(STORAGE_KEYS.user);
        storage.remove(STORAGE_KEYS.cart);
        storage.remove(STORAGE_KEYS.wishlist);
        
        showToast('Logged out successfully', 'success');
        
        // Redirect to home page
        setTimeout(() => {
            window.location.href = '/';
        }, 1000);
    } catch (error) {
        console.error('Logout error:', error);
        showToast('Error logging out', 'error');
    }
}

/**
 * Add to cart
 */
function addToCart(productId, variantId = null, quantity = 1) {
    const cart = storage.get(STORAGE_KEYS.cart, []);
    
    // Check if item already exists
    const existingItemIndex = cart.findIndex(item => 
        item.productId === productId && item.variantId === variantId
    );
    
    if (existingItemIndex > -1) {
        // Update quantity
        cart[existingItemIndex].quantity += quantity;
    } else {
        // Add new item
        cart.push({
            productId,
            variantId,
            quantity,
            addedAt: new Date().toISOString()
        });
    }
    
    storage.set(STORAGE_KEYS.cart, cart);
    
    // Dispatch cart update event
    document.dispatchEvent(new CustomEvent(EVENTS.cartUpdate));
    
    showToast('Product added to cart!', 'success');
}

/**
 * Add to wishlist
 */
function addToWishlist(productId) {
    const wishlist = storage.get(STORAGE_KEYS.wishlist, []);
    
    if (!wishlist.includes(productId)) {
        wishlist.push(productId);
        storage.set(STORAGE_KEYS.wishlist, wishlist);
        
        // Dispatch wishlist update event
        document.dispatchEvent(new CustomEvent(EVENTS.wishlistUpdate));
        
        showToast('Product added to wishlist!', 'success');
    } else {
        showToast('Product already in wishlist', 'info');
    }
}

/**
 * Add to compare
 */
function addToCompare(productId) {
    const compare = storage.get(STORAGE_KEYS.compare, []);
    
    if (compare.length >= 4) {
        showToast('You can compare up to 4 products at a time', 'warning');
        return;
    }
    
    if (!compare.includes(productId)) {
        compare.push(productId);
        storage.set(STORAGE_KEYS.compare, compare);
        
        // Dispatch compare update event
        document.dispatchEvent(new CustomEvent(EVENTS.compareUpdate));
        
        showToast('Product added to comparison!', 'success');
    } else {
        showToast('Product already in comparison', 'info');
    }
}

/**
 * Handle form validation
 */
function validateForm(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    
    inputs.forEach(input => {
        const value = input.value.trim();
        const type = input.type;
        
        // Remove existing error styling
        input.classList.remove('is-invalid');
        
        // Check if required field is empty
        if (!value) {
            input.classList.add('is-invalid');
            isValid = false;
            return;
        }
        
        // Validate email
        if (type === 'email' && !isValidEmail(value)) {
            input.classList.add('is-invalid');
            isValid = false;
        }
        
        // Validate phone
        if (input.name === 'phone' && !isValidPhone(value)) {
            input.classList.add('is-invalid');
            isValid = false;
        }
        
        // Validate password
        if (type === 'password' && value.length < VALIDATION_RULES.password.minLength) {
            input.classList.add('is-invalid');
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * Handle image loading errors
 */
document.addEventListener('error', function(e) {
    if (e.target.tagName === 'IMG') {
        e.target.src = '/assets/images/no-image.png';
    }
}, true);

/**
 * Handle network status
 */
window.addEventListener('online', function() {
    showToast('Connection restored', 'success');
});

window.addEventListener('offline', function() {
    showToast('Connection lost. Some features may not work.', 'warning');
});

// Export functions for global use
window.logout = logout;
window.addToCart = addToCart;
window.addToWishlist = addToWishlist;
window.addToCompare = addToCompare;
window.changeLanguage = changeLanguage;
window.showToast = showToast;

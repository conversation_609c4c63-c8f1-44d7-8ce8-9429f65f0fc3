/**
 * API Helper Functions
 * Musanze Marketplace
 */

class API {
    constructor() {
        this.baseURL = '/api';
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
    }
    
    /**
     * Make API request
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}/${endpoint.replace(/^\//, '')}`;
        
        const config = {
            headers: { ...this.defaultHeaders, ...options.headers },
            ...options
        };
        
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }
    
    /**
     * GET request
     */
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        
        return this.request(url, {
            method: 'GET'
        });
    }
    
    /**
     * POST request
     */
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
    
    /**
     * PUT request
     */
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }
    
    /**
     * DELETE request
     */
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }
    
    // Authentication endpoints
    async login(email, password) {
        return this.post('auth/login', { email, password });
    }
    
    async register(userData) {
        return this.post('auth/register', userData);
    }
    
    async logout() {
        return this.post('auth/logout');
    }
    
    async getCurrentUser() {
        return this.get('auth/me');
    }
    
    // Product endpoints
    async getProducts(params = {}) {
        return this.get('products', params);
    }
    
    async getProduct(id) {
        return this.get(`products/${id}`);
    }
    
    async createProduct(productData) {
        return this.post('products', productData);
    }
    
    async updateProduct(id, productData) {
        return this.put(`products/${id}`, productData);
    }
    
    async deleteProduct(id) {
        return this.delete(`products/${id}`);
    }
    
    async compareProduct(id) {
        return this.get(`products/${id}/compare`);
    }
    
    // Category endpoints
    async getCategories() {
        return this.get('categories');
    }
    
    async getCategory(id) {
        return this.get(`categories/${id}`);
    }
    
    // Vendor endpoints
    async getVendors(params = {}) {
        return this.get('vendors', params);
    }
    
    async getVendor(id) {
        return this.get(`vendors/${id}`);
    }
    
    async updateVendorProfile(data) {
        return this.put('vendors/profile', data);
    }
    
    // Order endpoints
    async getOrders(params = {}) {
        return this.get('orders', params);
    }
    
    async getOrder(id) {
        return this.get(`orders/${id}`);
    }
    
    async createOrder(orderData) {
        return this.post('orders', orderData);
    }
    
    async updateOrderStatus(id, status) {
        return this.put(`orders/${id}/status`, { status });
    }
    
    // User endpoints
    async getUsers(params = {}) {
        return this.get('users', params);
    }
    
    async updateProfile(data) {
        return this.put('users/profile', data);
    }
    
    async changePassword(currentPassword, newPassword) {
        return this.put('users/password', {
            current_password: currentPassword,
            new_password: newPassword
        });
    }
}

// Create global API instance
const api = new API();

/**
 * Handle API errors globally
 */
window.addEventListener('unhandledrejection', function(event) {
    if (event.reason && event.reason.message) {
        console.error('Unhandled API error:', event.reason.message);
        
        // Show user-friendly error message
        if (event.reason.message.includes('401') || event.reason.message.includes('Unauthorized')) {
            showToast('Please log in to continue', 'warning');
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
        } else if (event.reason.message.includes('403') || event.reason.message.includes('Forbidden')) {
            showToast('You do not have permission to perform this action', 'error');
        } else if (event.reason.message.includes('404') || event.reason.message.includes('Not Found')) {
            showToast('The requested resource was not found', 'error');
        } else if (event.reason.message.includes('500') || event.reason.message.includes('Internal Server Error')) {
            showToast('Server error. Please try again later', 'error');
        } else {
            showToast('An error occurred. Please try again', 'error');
        }
    }
});

/**
 * Utility functions for API responses
 */
function handleApiResponse(response, successCallback, errorCallback) {
    if (response.success) {
        if (successCallback) successCallback(response.data);
    } else {
        if (errorCallback) {
            errorCallback(response.message);
        } else {
            showToast(response.message || 'An error occurred', 'error');
        }
    }
}

/**
 * Show loading state
 */
function showLoading(element, text = 'Loading...') {
    if (typeof element === 'string') {
        element = document.getElementById(element);
    }
    
    if (element) {
        element.innerHTML = `
            <div class="text-center py-3">
                <div class="spinner-border spinner-border-sm me-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                ${text}
            </div>
        `;
    }
}

/**
 * Show error state
 */
function showError(element, message = 'An error occurred', retryCallback = null) {
    if (typeof element === 'string') {
        element = document.getElementById(element);
    }
    
    if (element) {
        const retryButton = retryCallback ? 
            `<button class="btn btn-sm btn-primary mt-2" onclick="(${retryCallback})()">Try Again</button>` : '';
        
        element.innerHTML = `
            <div class="text-center py-3">
                <i class="fas fa-exclamation-triangle text-warning mb-2"></i>
                <p class="text-muted mb-0">${message}</p>
                ${retryButton}
            </div>
        `;
    }
}

/**
 * Show empty state
 */
function showEmpty(element, message = 'No data available', actionButton = null) {
    if (typeof element === 'string') {
        element = document.getElementById(element);
    }
    
    if (element) {
        const button = actionButton ? 
            `<button class="btn btn-primary mt-2" onclick="${actionButton.onclick}">${actionButton.text}</button>` : '';
        
        element.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <p class="text-muted">${message}</p>
                ${button}
            </div>
        `;
    }
}

/**
 * Format API errors for display
 */
function formatApiError(error) {
    if (typeof error === 'string') {
        return error;
    }
    
    if (error.errors && Array.isArray(error.errors)) {
        return error.errors.join(', ');
    }
    
    if (error.message) {
        return error.message;
    }
    
    return 'An unexpected error occurred';
}

/**
 * Debounce function for API calls
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Cache for API responses
 */
class APICache {
    constructor(ttl = 300000) { // 5 minutes default TTL
        this.cache = new Map();
        this.ttl = ttl;
    }
    
    set(key, data) {
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }
    
    get(key) {
        const item = this.cache.get(key);
        if (!item) return null;
        
        if (Date.now() - item.timestamp > this.ttl) {
            this.cache.delete(key);
            return null;
        }
        
        return item.data;
    }
    
    clear() {
        this.cache.clear();
    }
    
    delete(key) {
        this.cache.delete(key);
    }
}

// Create global cache instance
const apiCache = new APICache();

/**
 * Cached API request
 */
async function cachedApiRequest(key, apiCall) {
    const cached = apiCache.get(key);
    if (cached) {
        return cached;
    }
    
    try {
        const response = await apiCall();
        apiCache.set(key, response);
        return response;
    } catch (error) {
        throw error;
    }
}

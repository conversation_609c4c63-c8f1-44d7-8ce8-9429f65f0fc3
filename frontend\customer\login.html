<?php include '../shared/header.html'; ?>

<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-body p-5">
                    <!-- Logo and Title -->
                    <div class="text-center mb-4">
                        <img src="/assets/images/logo.png" alt="Musanze Marketplace" height="60" class="mb-3">
                        <h2 class="h4 mb-2">Welcome Back</h2>
                        <p class="text-muted">Sign in to your account</p>
                    </div>
                    
                    <!-- Login Form -->
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3 d-flex justify-content-between align-items-center">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="rememberMe" name="remember">
                                <label class="form-check-label" for="rememberMe">
                                    Remember me
                                </label>
                            </div>
                            <a href="/forgot-password" class="text-decoration-none">Forgot password?</a>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            Sign In
                        </button>
                    </form>
                    
                    <!-- Divider -->
                    <div class="text-center mb-3">
                        <span class="text-muted">or</span>
                    </div>
                    
                    <!-- Social Login (Optional) -->
                    <div class="d-grid gap-2 mb-4">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fab fa-google me-2"></i>Continue with Google
                        </button>
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fab fa-facebook me-2"></i>Continue with Facebook
                        </button>
                    </div>
                    
                    <!-- Register Link -->
                    <div class="text-center">
                        <p class="mb-0">Don't have an account? 
                            <a href="/register" class="text-decoration-none">Create one here</a>
                        </p>
                    </div>
                    
                    <!-- Vendor Registration -->
                    <div class="text-center mt-3 pt-3 border-top">
                        <p class="text-muted mb-2">Are you a business owner?</p>
                        <a href="/register?role=vendor" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-store me-2"></i>Register as Vendor
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Features -->
            <div class="row mt-4 text-center">
                <div class="col-4">
                    <i class="fas fa-shield-alt fa-2x text-primary mb-2"></i>
                    <p class="small text-muted">Secure Login</p>
                </div>
                <div class="col-4">
                    <i class="fas fa-search fa-2x text-primary mb-2"></i>
                    <p class="small text-muted">Price Comparison</p>
                </div>
                <div class="col-4">
                    <i class="fas fa-truck fa-2x text-primary mb-2"></i>
                    <p class="small text-muted">Fast Delivery</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Redirect if already authenticated
redirectIfAuthenticated();

document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    
    togglePassword.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        
        const icon = this.querySelector('i');
        icon.classList.toggle('fa-eye');
        icon.classList.toggle('fa-eye-slash');
    });
    
    // Auto-focus email field
    document.getElementById('email').focus();
    
    // Handle demo login (for testing)
    if (window.location.search.includes('demo=true')) {
        document.getElementById('email').value = '<EMAIL>';
        document.getElementById('password').value = 'demo123';
    }
});
</script>

<?php include '../shared/footer.html'; ?>

<?php include '../shared/header.html'; ?>

<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-body p-5">
                    <!-- Logo and Title -->
                    <div class="text-center mb-4">
                        <img src="/assets/images/logo.png" alt="Musanze Marketplace" height="60" class="mb-3">
                        <h2 class="h4 mb-2">Create Your Account</h2>
                        <p class="text-muted">Join Musanze Marketplace today</p>
                    </div>
                    
                    <!-- Account Type Selection -->
                    <div class="mb-4">
                        <label class="form-label">I want to:</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="radio" class="btn-check" name="accountType" id="customerAccount" value="customer" checked>
                                <label class="btn btn-outline-primary w-100" for="customerAccount">
                                    <i class="fas fa-user d-block mb-2"></i>
                                    Shop Products
                                    <small class="d-block text-muted">Customer Account</small>
                                </label>
                            </div>
                            <div class="col-6">
                                <input type="radio" class="btn-check" name="accountType" id="vendorAccount" value="vendor">
                                <label class="btn btn-outline-primary w-100" for="vendorAccount">
                                    <i class="fas fa-store d-block mb-2"></i>
                                    Sell Products
                                    <small class="d-block text-muted">Vendor Account</small>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Registration Form -->
                    <form id="registrationForm">
                        <input type="hidden" name="role" id="roleInput" value="customer">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="firstName" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="firstName" name="first_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="lastName" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="lastName" name="last_name" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="form-text">We'll never share your email with anyone else.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number *</label>
                            <input type="tel" class="form-control" id="phone" name="phone" placeholder="+250 7XX XXX XXX" required>
                            <div class="form-text">Enter your Rwanda phone number</div>
                        </div>
                        
                        <!-- Vendor-specific fields -->
                        <div id="vendorFields" style="display: none;">
                            <div class="mb-3">
                                <label for="businessName" class="form-label">Business Name *</label>
                                <input type="text" class="form-control" id="businessName" name="business_name">
                            </div>
                            
                            <div class="mb-3">
                                <label for="businessAddress" class="form-label">Business Address *</label>
                                <textarea class="form-control" id="businessAddress" name="business_address" rows="2"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="businessLicense" class="form-label">Business License Number</label>
                                <input type="text" class="form-control" id="businessLicense" name="business_license">
                                <div class="form-text">Optional but recommended for verification</div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Password *</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div id="passwordStrength"></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">Confirm Password *</label>
                            <input type="password" class="form-control" id="confirmPassword" name="confirm_password" required>
                            <div id="passwordMatch"></div>
                        </div>
                        
                        <!-- Terms and Conditions -->
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="agreeTerms" name="agree_terms" required>
                                <label class="form-check-label" for="agreeTerms">
                                    I agree to the <a href="/terms" target="_blank">Terms of Service</a> and 
                                    <a href="/privacy" target="_blank">Privacy Policy</a>
                                </label>
                            </div>
                        </div>
                        
                        <!-- Marketing consent -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="marketingConsent" name="marketing_consent">
                                <label class="form-check-label" for="marketingConsent">
                                    I would like to receive promotional emails and updates about new products and deals
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            Create Account
                        </button>
                    </form>
                    
                    <!-- Login Link -->
                    <div class="text-center">
                        <p class="mb-0">Already have an account? 
                            <a href="/login" class="text-decoration-none">Sign in here</a>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Benefits -->
            <div class="row mt-4 text-center">
                <div class="col-md-4 mb-3">
                    <div class="card h-100 border-0">
                        <div class="card-body">
                            <i class="fas fa-search fa-3x text-primary mb-3"></i>
                            <h6>Compare Prices</h6>
                            <p class="small text-muted">Find the best deals from multiple vendors in Musanze</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card h-100 border-0">
                        <div class="card-body">
                            <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                            <h6>Secure Shopping</h6>
                            <p class="small text-muted">Shop with confidence from verified local vendors</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card h-100 border-0">
                        <div class="card-body">
                            <i class="fas fa-truck fa-3x text-primary mb-3"></i>
                            <h6>Fast Delivery</h6>
                            <p class="small text-muted">Quick delivery within Musanze District</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Redirect if already authenticated
redirectIfAuthenticated();

document.addEventListener('DOMContentLoaded', function() {
    // Handle account type selection
    const accountTypeRadios = document.querySelectorAll('input[name="accountType"]');
    const vendorFields = document.getElementById('vendorFields');
    const roleInput = document.getElementById('roleInput');
    
    accountTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            const isVendor = this.value === 'vendor';
            vendorFields.style.display = isVendor ? 'block' : 'none';
            roleInput.value = this.value;
            
            // Update required fields
            const vendorInputs = vendorFields.querySelectorAll('input, textarea');
            vendorInputs.forEach(input => {
                if (isVendor && input.labels[0].textContent.includes('*')) {
                    input.required = true;
                } else {
                    input.required = false;
                }
            });
        });
    });
    
    // Check URL parameter for role
    const urlParams = new URLSearchParams(window.location.search);
    const roleParam = urlParams.get('role');
    if (roleParam === 'vendor') {
        document.getElementById('vendorAccount').checked = true;
        document.getElementById('vendorAccount').dispatchEvent(new Event('change'));
    }
    
    // Toggle password visibility
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    
    togglePassword.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        
        const icon = this.querySelector('i');
        icon.classList.toggle('fa-eye');
        icon.classList.toggle('fa-eye-slash');
    });
    
    // Password confirmation validation
    const confirmPasswordInput = document.getElementById('confirmPassword');
    const passwordMatchDiv = document.getElementById('passwordMatch');
    
    function checkPasswordMatch() {
        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        
        if (confirmPassword.length === 0) {
            passwordMatchDiv.innerHTML = '';
            return;
        }
        
        if (password === confirmPassword) {
            passwordMatchDiv.innerHTML = '<small class="text-success"><i class="fas fa-check me-1"></i>Passwords match</small>';
        } else {
            passwordMatchDiv.innerHTML = '<small class="text-danger"><i class="fas fa-times me-1"></i>Passwords do not match</small>';
        }
    }
    
    confirmPasswordInput.addEventListener('input', checkPasswordMatch);
    passwordInput.addEventListener('input', checkPasswordMatch);
    
    // Combine first and last name for the name field
    const firstNameInput = document.getElementById('firstName');
    const lastNameInput = document.getElementById('lastName');
    
    function updateFullName() {
        const firstName = firstNameInput.value.trim();
        const lastName = lastNameInput.value.trim();
        const fullName = `${firstName} ${lastName}`.trim();
        
        // Create or update hidden name field
        let nameInput = document.querySelector('input[name="name"]');
        if (!nameInput) {
            nameInput = document.createElement('input');
            nameInput.type = 'hidden';
            nameInput.name = 'name';
            document.getElementById('registrationForm').appendChild(nameInput);
        }
        nameInput.value = fullName;
    }
    
    firstNameInput.addEventListener('input', updateFullName);
    lastNameInput.addEventListener('input', updateFullName);
    
    // Auto-focus first name field
    firstNameInput.focus();
    
    // Form validation
    const form = document.getElementById('registrationForm');
    form.addEventListener('submit', function(e) {
        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        
        if (password !== confirmPassword) {
            e.preventDefault();
            showToast('Passwords do not match', 'error');
            confirmPasswordInput.focus();
            return;
        }
        
        if (password.length < 6) {
            e.preventDefault();
            showToast('Password must be at least 6 characters long', 'error');
            passwordInput.focus();
            return;
        }
    });
});
</script>

<?php include '../shared/footer.html'; ?>

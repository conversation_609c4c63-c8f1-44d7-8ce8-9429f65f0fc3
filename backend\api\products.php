<?php
/**
 * Products API Endpoints
 */

require_once '../models/Product.php';
require_once '../middleware/auth.php';

// Get remaining URI parts
$productId = isset($uri_parts[1]) ? $uri_parts[1] : null;
$action = isset($uri_parts[2]) ? $uri_parts[2] : null;

switch ($method) {
    case 'GET':
        if ($productId && $action === 'compare') {
            handleCompareProduct($productId);
        } elseif ($productId) {
            handleGetProduct($productId);
        } else {
            handleGetProducts();
        }
        break;
        
    case 'POST':
        requireAuth(['vendor', 'admin']);
        handleCreateProduct();
        break;
        
    case 'PUT':
        requireAuth(['vendor', 'admin']);
        if (!$productId) {
            sendError('Product ID is required', 400);
        }
        handleUpdateProduct($productId);
        break;
        
    case 'DELETE':
        requireAuth(['vendor', 'admin']);
        if (!$productId) {
            sendError('Product ID is required', 400);
        }
        handleDeleteProduct($productId);
        break;
        
    default:
        sendError('Method not allowed', 405);
}

function handleGetProducts() {
    try {
        $productModel = new Product();
        
        // Get query parameters
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 20;
        
        $filters = [
            'category_id' => $_GET['category'] ?? null,
            'vendor_id' => $_GET['vendor'] ?? null,
            'search' => $_GET['q'] ?? null,
            'min_price' => $_GET['min_price'] ?? null,
            'max_price' => $_GET['max_price'] ?? null,
            'status' => $_GET['status'] ?? 'active',
            'featured' => $_GET['featured'] ?? null
        ];
        
        $sort = $_GET['sort'] ?? 'created_at';
        $order = $_GET['order'] ?? 'desc';
        
        $result = $productModel->getAll($page, $limit, $filters, $sort, $order);
        sendResponse($result);
        
    } catch (Exception $e) {
        error_log('Get products error: ' . $e->getMessage());
        sendError('Failed to get products', 500);
    }
}

function handleGetProduct($productId) {
    try {
        $productModel = new Product();
        $product = $productModel->findById($productId);
        
        if (!$product) {
            sendError('Product not found', 404);
        }
        
        // Get product variants
        $variants = $productModel->getVariants($productId);
        $product['variants'] = $variants;
        
        // Get product reviews
        $reviews = $productModel->getReviews($productId);
        $product['reviews'] = $reviews;
        
        sendResponse(['product' => $product]);
        
    } catch (Exception $e) {
        error_log('Get product error: ' . $e->getMessage());
        sendError('Failed to get product', 500);
    }
}

function handleCompareProduct($productId) {
    try {
        $productModel = new Product();
        $product = $productModel->findById($productId);
        
        if (!$product) {
            sendError('Product not found', 404);
        }
        
        // Find similar products from other vendors
        $similarProducts = $productModel->findSimilar($product['name'], $product['category_id'], $productId);
        
        sendResponse([
            'product' => $product,
            'similar_products' => $similarProducts
        ]);
        
    } catch (Exception $e) {
        error_log('Compare product error: ' . $e->getMessage());
        sendError('Failed to compare product', 500);
    }
}

function handleCreateProduct() {
    global $input;
    
    // Validate input
    $required = ['name', 'category_id', 'description', 'variants'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            sendError("Field '{$field}' is required", 400);
        }
    }
    
    // Validate variants
    if (!is_array($input['variants']) || empty($input['variants'])) {
        sendError('At least one product variant is required', 400);
    }
    
    try {
        $productModel = new Product();
        
        // Check vendor permission
        $vendorId = getCurrentUserId();
        if (getCurrentUserRole() === 'vendor') {
            // Vendors can only create products for themselves
            $input['vendor_id'] = $vendorId;
        } elseif (empty($input['vendor_id'])) {
            sendError('Vendor ID is required', 400);
        }
        
        $productData = [
            'vendor_id' => $input['vendor_id'],
            'category_id' => $input['category_id'],
            'name' => sanitize($input['name']),
            'slug' => generateSlug($input['name']),
            'description' => sanitize($input['description']),
            'short_description' => sanitize($input['short_description'] ?? ''),
            'sku' => sanitize($input['sku'] ?? ''),
            'brand' => sanitize($input['brand'] ?? ''),
            'model' => sanitize($input['model'] ?? ''),
            'specifications' => json_encode($input['specifications'] ?? []),
            'images' => json_encode($input['images'] ?? []),
            'status' => $input['status'] ?? 'draft',
            'featured' => $input['featured'] ?? false,
            'meta_title' => sanitize($input['meta_title'] ?? ''),
            'meta_description' => sanitize($input['meta_description'] ?? '')
        ];
        
        $productId = $productModel->create($productData, $input['variants']);
        
        // Get created product
        $product = $productModel->findById($productId);
        
        sendResponse([
            'product' => $product,
            'message' => 'Product created successfully'
        ], 201);
        
    } catch (Exception $e) {
        error_log('Create product error: ' . $e->getMessage());
        sendError('Failed to create product', 500);
    }
}

function handleUpdateProduct($productId) {
    global $input;
    
    try {
        $productModel = new Product();
        $product = $productModel->findById($productId);
        
        if (!$product) {
            sendError('Product not found', 404);
        }
        
        // Check vendor permission
        if (getCurrentUserRole() === 'vendor' && $product['vendor_id'] != getCurrentUserId()) {
            sendError('You can only update your own products', 403);
        }
        
        $updateData = [];
        $allowedFields = ['name', 'category_id', 'description', 'short_description', 'sku', 'brand', 'model', 'specifications', 'images', 'status', 'featured', 'meta_title', 'meta_description'];
        
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                if (in_array($field, ['name', 'description', 'short_description', 'sku', 'brand', 'model', 'meta_title', 'meta_description'])) {
                    $updateData[$field] = sanitize($input[$field]);
                } elseif (in_array($field, ['specifications', 'images'])) {
                    $updateData[$field] = json_encode($input[$field]);
                } else {
                    $updateData[$field] = $input[$field];
                }
            }
        }
        
        if (isset($input['name'])) {
            $updateData['slug'] = generateSlug($input['name']);
        }
        
        $productModel->update($productId, $updateData);
        
        // Update variants if provided
        if (isset($input['variants'])) {
            $productModel->updateVariants($productId, $input['variants']);
        }
        
        // Get updated product
        $updatedProduct = $productModel->findById($productId);
        
        sendResponse([
            'product' => $updatedProduct,
            'message' => 'Product updated successfully'
        ]);
        
    } catch (Exception $e) {
        error_log('Update product error: ' . $e->getMessage());
        sendError('Failed to update product', 500);
    }
}

function handleDeleteProduct($productId) {
    try {
        $productModel = new Product();
        $product = $productModel->findById($productId);
        
        if (!$product) {
            sendError('Product not found', 404);
        }
        
        // Check vendor permission
        if (getCurrentUserRole() === 'vendor' && $product['vendor_id'] != getCurrentUserId()) {
            sendError('You can only delete your own products', 403);
        }
        
        $productModel->delete($productId);
        
        sendResponse(['message' => 'Product deleted successfully']);
        
    } catch (Exception $e) {
        error_log('Delete product error: ' . $e->getMessage());
        sendError('Failed to delete product', 500);
    }
}

function generateSlug($text) {
    $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $text)));
    return trim($slug, '-');
}
?>

<?php
/**
 * Authentication Middleware
 */

/**
 * Check if user is authenticated
 */
function isAuthenticated() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Get current user ID
 */
function getCurrentUserId() {
    return $_SESSION['user_id'] ?? null;
}

/**
 * Get current user role
 */
function getCurrentUserRole() {
    return $_SESSION['user_role'] ?? null;
}

/**
 * Require authentication
 */
function requireAuth($allowedRoles = []) {
    if (!isAuthenticated()) {
        sendError('Authentication required', 401);
    }
    
    if (!empty($allowedRoles)) {
        $userRole = getCurrentUserRole();
        if (!in_array($userRole, $allowedRoles)) {
            sendError('Insufficient permissions', 403);
        }
    }
}

/**
 * Check if user has specific role
 */
function hasRole($role) {
    return getCurrentUserRole() === $role;
}

/**
 * Check if user has any of the specified roles
 */
function hasAnyRole($roles) {
    $userRole = getCurrentUserRole();
    return in_array($userRole, $roles);
}

/**
 * Get current user data
 */
function getCurrentUser() {
    if (!isAuthenticated()) {
        return null;
    }
    
    try {
        require_once '../models/User.php';
        $userModel = new User();
        $user = $userModel->findById(getCurrentUserId());
        
        if ($user) {
            unset($user['password']);
        }
        
        return $user;
    } catch (Exception $e) {
        error_log('Get current user error: ' . $e->getMessage());
        return null;
    }
}

/**
 * Check if user owns resource
 */
function ownsResource($resourceUserId) {
    return getCurrentUserId() == $resourceUserId;
}

/**
 * Require admin role
 */
function requireAdmin() {
    requireAuth(['admin']);
}

/**
 * Require vendor role
 */
function requireVendor() {
    requireAuth(['vendor', 'admin']);
}

/**
 * Require customer role
 */
function requireCustomer() {
    requireAuth(['customer', 'vendor', 'admin']);
}
?>

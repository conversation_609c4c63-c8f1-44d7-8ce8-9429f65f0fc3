<?php
/**
 * User Model
 * Handles user data operations
 */

class User {
    private $db;
    private $table = 'users';
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Create a new user
     */
    public function create($data) {
        $sql = "INSERT INTO {$this->table} (name, email, phone, password, role, status, created_at, updated_at) 
                VALUES (:name, :email, :phone, :password, :role, :status, NOW(), NOW())";
        
        $params = [
            'name' => $data['name'],
            'email' => $data['email'],
            'phone' => $data['phone'],
            'password' => $data['password'],
            'role' => $data['role'] ?? 'customer',
            'status' => $data['status'] ?? 'active'
        ];
        
        $this->db->query($sql, $params);
        return $this->db->getConnection()->lastInsertId();
    }
    
    /**
     * Find user by ID
     */
    public function findById($id) {
        $sql = "SELECT * FROM {$this->table} WHERE id = :id";
        return $this->db->fetch($sql, ['id' => $id]);
    }
    
    /**
     * Find user by email
     */
    public function findByEmail($email) {
        $sql = "SELECT * FROM {$this->table} WHERE email = :email";
        return $this->db->fetch($sql, ['email' => $email]);
    }
    
    /**
     * Find user by phone
     */
    public function findByPhone($phone) {
        $sql = "SELECT * FROM {$this->table} WHERE phone = :phone";
        return $this->db->fetch($sql, ['phone' => $phone]);
    }
    
    /**
     * Update user
     */
    public function update($id, $data) {
        $setClause = [];
        $params = ['id' => $id];
        
        foreach ($data as $key => $value) {
            if ($key !== 'id') {
                $setClause[] = "{$key} = :{$key}";
                $params[$key] = $value;
            }
        }
        
        $params['updated_at'] = date('Y-m-d H:i:s');
        $setClause[] = "updated_at = :updated_at";
        
        $sql = "UPDATE {$this->table} SET " . implode(', ', $setClause) . " WHERE id = :id";
        return $this->db->query($sql, $params);
    }
    
    /**
     * Update last login timestamp
     */
    public function updateLastLogin($id) {
        $sql = "UPDATE {$this->table} SET updated_at = NOW() WHERE id = :id";
        return $this->db->query($sql, ['id' => $id]);
    }
    
    /**
     * Get all users with pagination
     */
    public function getAll($page = 1, $limit = 20, $filters = []) {
        $offset = ($page - 1) * $limit;
        $whereClause = [];
        $params = [];
        
        // Apply filters
        if (!empty($filters['role'])) {
            $whereClause[] = "role = :role";
            $params['role'] = $filters['role'];
        }
        
        if (!empty($filters['status'])) {
            $whereClause[] = "status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (!empty($filters['search'])) {
            $whereClause[] = "(name LIKE :search OR email LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        $whereSQL = !empty($whereClause) ? 'WHERE ' . implode(' AND ', $whereClause) : '';
        
        // Get total count
        $countSQL = "SELECT COUNT(*) as total FROM {$this->table} {$whereSQL}";
        $total = $this->db->fetch($countSQL, $params)['total'];
        
        // Get users
        $sql = "SELECT id, name, email, phone, role, status, created_at, updated_at 
                FROM {$this->table} {$whereSQL} 
                ORDER BY created_at DESC 
                LIMIT :limit OFFSET :offset";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        $users = $this->db->fetchAll($sql, $params);
        
        return [
            'users' => $users,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $total,
                'total_pages' => ceil($total / $limit)
            ]
        ];
    }
    
    /**
     * Delete user
     */
    public function delete($id) {
        $sql = "DELETE FROM {$this->table} WHERE id = :id";
        return $this->db->query($sql, ['id' => $id]);
    }
    
    /**
     * Change user status
     */
    public function changeStatus($id, $status) {
        $validStatuses = ['active', 'inactive', 'suspended'];
        if (!in_array($status, $validStatuses)) {
            throw new Exception('Invalid status');
        }
        
        return $this->update($id, ['status' => $status]);
    }
    
    /**
     * Change password
     */
    public function changePassword($id, $newPassword) {
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        return $this->update($id, ['password' => $hashedPassword]);
    }
    
    /**
     * Get user statistics
     */
    public function getStatistics() {
        $sql = "SELECT 
                    COUNT(*) as total_users,
                    SUM(CASE WHEN role = 'customer' THEN 1 ELSE 0 END) as customers,
                    SUM(CASE WHEN role = 'vendor' THEN 1 ELSE 0 END) as vendors,
                    SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admins,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
                    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as new_today,
                    SUM(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as new_this_week
                FROM {$this->table}";
        
        return $this->db->fetch($sql);
    }
    
    /**
     * Verify email
     */
    public function verifyEmail($id) {
        return $this->update($id, ['email_verified_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * Verify phone
     */
    public function verifyPhone($id) {
        return $this->update($id, ['phone_verified_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * Check if user exists
     */
    public function exists($field, $value) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE {$field} = :value";
        $result = $this->db->fetch($sql, ['value' => $value]);
        return $result['count'] > 0;
    }
}
?>

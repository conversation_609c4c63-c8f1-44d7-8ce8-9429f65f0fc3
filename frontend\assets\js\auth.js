/**
 * Authentication JavaScript
 * Musanze Marketplace
 */

class Auth {
    constructor() {
        this.currentUser = null;
        this.isAuthenticated = false;
        this.init();
    }
    
    async init() {
        // Check if user is already authenticated
        await this.checkAuthStatus();
        this.updateUI();
    }
    
    async checkAuthStatus() {
        try {
            const response = await api.getCurrentUser();
            if (response.success) {
                this.currentUser = response.data.user;
                this.isAuthenticated = true;
                return true;
            }
        } catch (error) {
            this.currentUser = null;
            this.isAuthenticated = false;
        }
        return false;
    }
    
    async login(email, password) {
        try {
            const response = await api.login(email, password);
            if (response.success) {
                this.currentUser = response.data.user;
                this.isAuthenticated = true;
                this.updateUI();
                return response;
            }
            throw new Error(response.message);
        } catch (error) {
            throw error;
        }
    }
    
    async register(userData) {
        try {
            const response = await api.register(userData);
            if (response.success) {
                this.currentUser = response.data.user;
                this.isAuthenticated = true;
                this.updateUI();
                return response;
            }
            throw new Error(response.message);
        } catch (error) {
            throw error;
        }
    }
    
    async logout() {
        try {
            await api.logout();
        } catch (error) {
            console.error('Logout API call failed:', error);
        } finally {
            this.currentUser = null;
            this.isAuthenticated = false;
            this.updateUI();
            window.location.href = '/';
        }
    }
    
    updateUI() {
        const loggedInMenu = document.getElementById('loggedInMenu');
        const loggedOutMenu = document.getElementById('loggedOutMenu');
        const userName = document.getElementById('userName');
        const vendorDashboardLink = document.getElementById('vendorDashboardLink');
        const adminDashboardLink = document.getElementById('adminDashboardLink');
        
        if (this.isAuthenticated && this.currentUser) {
            // Show logged in menu
            if (loggedInMenu) loggedInMenu.style.display = 'block';
            if (loggedOutMenu) loggedOutMenu.style.display = 'none';
            if (userName) userName.textContent = this.currentUser.name;
            
            // Show role-specific links
            if (vendorDashboardLink && ['vendor', 'admin'].includes(this.currentUser.role)) {
                vendorDashboardLink.style.display = 'block';
            }
            if (adminDashboardLink && this.currentUser.role === 'admin') {
                adminDashboardLink.style.display = 'block';
            }
        } else {
            // Show logged out menu
            if (loggedInMenu) loggedInMenu.style.display = 'none';
            if (loggedOutMenu) loggedOutMenu.style.display = 'block';
            if (userName) userName.textContent = 'Account';
            
            // Hide role-specific links
            if (vendorDashboardLink) vendorDashboardLink.style.display = 'none';
            if (adminDashboardLink) adminDashboardLink.style.display = 'none';
        }
    }
    
    requireAuth(allowedRoles = []) {
        if (!this.isAuthenticated) {
            window.location.href = '/login';
            return false;
        }
        
        if (allowedRoles.length > 0 && !allowedRoles.includes(this.currentUser.role)) {
            showToast('You do not have permission to access this page', 'error');
            window.location.href = '/';
            return false;
        }
        
        return true;
    }
    
    hasRole(role) {
        return this.isAuthenticated && this.currentUser && this.currentUser.role === role;
    }
    
    hasAnyRole(roles) {
        return this.isAuthenticated && this.currentUser && roles.includes(this.currentUser.role);
    }
    
    isVendor() {
        return this.hasRole('vendor') || this.hasRole('admin');
    }
    
    isAdmin() {
        return this.hasRole('admin');
    }
    
    isCustomer() {
        return this.hasRole('customer');
    }
}

// Create global auth instance
const auth = new Auth();

// Global logout function
function logout() {
    auth.logout();
}

/**
 * Login form handler
 */
function handleLoginForm() {
    const form = document.getElementById('loginForm');
    if (!form) return;
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        
        try {
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Logging in...';
            
            const formData = new FormData(form);
            const email = formData.get('email');
            const password = formData.get('password');
            
            await auth.login(email, password);
            
            showToast('Login successful!', 'success');
            
            // Redirect based on user role
            const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || 
                               (auth.isVendor() ? '/vendor' : 
                                auth.isAdmin() ? '/admin' : '/');
            
            window.location.href = redirectUrl;
            
        } catch (error) {
            showToast(error.message || 'Login failed', 'error');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    });
}

/**
 * Registration form handler
 */
function handleRegistrationForm() {
    const form = document.getElementById('registrationForm');
    if (!form) return;
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        
        try {
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Creating account...';
            
            const formData = new FormData(form);
            const userData = {
                name: formData.get('name'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                password: formData.get('password'),
                role: formData.get('role') || 'customer'
            };
            
            // Validate password confirmation
            const confirmPassword = formData.get('confirm_password');
            if (userData.password !== confirmPassword) {
                throw new Error('Passwords do not match');
            }
            
            await auth.register(userData);
            
            showToast('Registration successful!', 'success');
            
            // Redirect based on user role
            const redirectUrl = userData.role === 'vendor' ? '/vendor' : '/';
            window.location.href = redirectUrl;
            
        } catch (error) {
            showToast(error.message || 'Registration failed', 'error');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    });
}

/**
 * Password strength checker
 */
function checkPasswordStrength(password) {
    let strength = 0;
    const checks = {
        length: password.length >= 8,
        lowercase: /[a-z]/.test(password),
        uppercase: /[A-Z]/.test(password),
        numbers: /\d/.test(password),
        special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };
    
    strength = Object.values(checks).filter(Boolean).length;
    
    return {
        score: strength,
        checks: checks,
        level: strength < 2 ? 'weak' : strength < 4 ? 'medium' : 'strong'
    };
}

/**
 * Update password strength indicator
 */
function updatePasswordStrength(passwordInput, indicatorElement) {
    const password = passwordInput.value;
    const strength = checkPasswordStrength(password);
    
    if (!indicatorElement) return;
    
    const colors = {
        weak: 'danger',
        medium: 'warning',
        strong: 'success'
    };
    
    const labels = {
        weak: 'Weak',
        medium: 'Medium',
        strong: 'Strong'
    };
    
    if (password.length === 0) {
        indicatorElement.innerHTML = '';
        return;
    }
    
    indicatorElement.innerHTML = `
        <div class="progress mt-2" style="height: 5px;">
            <div class="progress-bar bg-${colors[strength.level]}" 
                 style="width: ${(strength.score / 5) * 100}%"></div>
        </div>
        <small class="text-${colors[strength.level]}">
            Password strength: ${labels[strength.level]}
        </small>
    `;
}

/**
 * Initialize authentication features
 */
document.addEventListener('DOMContentLoaded', function() {
    // Handle login form
    handleLoginForm();
    
    // Handle registration form
    handleRegistrationForm();
    
    // Password strength checker
    const passwordInput = document.getElementById('password');
    const strengthIndicator = document.getElementById('passwordStrength');
    
    if (passwordInput && strengthIndicator) {
        passwordInput.addEventListener('input', function() {
            updatePasswordStrength(passwordInput, strengthIndicator);
        });
    }
    
    // Phone number formatting
    const phoneInputs = document.querySelectorAll('input[type="tel"], input[name="phone"]');
    phoneInputs.forEach(input => {
        input.addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            
            // Rwanda phone number formatting
            if (value.startsWith('250')) {
                value = '+' + value;
            } else if (value.startsWith('07') || value.startsWith('78') || value.startsWith('79')) {
                value = '+250' + value.substring(1);
            } else if (value.length === 9 && (value.startsWith('7') || value.startsWith('8'))) {
                value = '+250' + value;
            }
            
            this.value = value;
        });
    });
    
    // Auto-hide alerts
    const alerts = document.querySelectorAll('.alert[data-auto-hide]');
    alerts.forEach(alert => {
        const delay = parseInt(alert.dataset.autoHide) || 5000;
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => alert.remove(), 300);
        }, delay);
    });
});

/**
 * Protect pages that require authentication
 */
function protectPage(allowedRoles = []) {
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(() => {
            auth.requireAuth(allowedRoles);
        }, 100);
    });
}

/**
 * Redirect if already authenticated
 */
function redirectIfAuthenticated(redirectUrl = '/') {
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(() => {
            if (auth.isAuthenticated) {
                window.location.href = redirectUrl;
            }
        }, 100);
    });
}

<?php
/**
 * Database Setup Script
 * Run this file to create the database and tables
 */

// Include configuration
require_once '../backend/config/config.php';

// Database connection for setup (without selecting database)
try {
    $setupConnection = new PDO(
        "mysql:host=" . DatabaseConfig::DB_HOST . ";charset=" . DatabaseConfig::DB_CHARSET,
        DatabaseConfig::DB_USER,
        DatabaseConfig::DB_PASS,
        DatabaseConfig::OPTIONS
    );
    
    echo "<h2>Database Setup for Musanze Marketplace</h2>";
    echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 20px;'>";
    
    // Read and execute schema
    $schemaFile = __DIR__ . '/schema.sql';
    if (!file_exists($schemaFile)) {
        throw new Exception("Schema file not found: " . $schemaFile);
    }
    
    $schema = file_get_contents($schemaFile);
    
    // Split the schema into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $schema)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    echo "<h3>Executing Database Setup...</h3>";
    echo "<ul>";
    
    foreach ($statements as $statement) {
        try {
            $setupConnection->exec($statement);
            
            // Extract table/database name for display
            if (preg_match('/CREATE\s+(DATABASE|TABLE)\s+(?:IF\s+NOT\s+EXISTS\s+)?(\w+)/i', $statement, $matches)) {
                $type = strtolower($matches[1]);
                $name = $matches[2];
                echo "<li style='color: green;'>✓ Created {$type}: <strong>{$name}</strong></li>";
            } elseif (preg_match('/INSERT\s+INTO\s+(\w+)/i', $statement, $matches)) {
                $table = $matches[1];
                echo "<li style='color: blue;'>✓ Inserted data into: <strong>{$table}</strong></li>";
            }
        } catch (PDOException $e) {
            echo "<li style='color: red;'>✗ Error: " . $e->getMessage() . "</li>";
        }
    }
    
    echo "</ul>";
    
    // Test connection to the new database
    try {
        $testDb = DatabaseConfig::getInstance();
        echo "<h3 style='color: green;'>✓ Database setup completed successfully!</h3>";
        
        // Show database info
        $tables = $testDb->getConnection()->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        echo "<h4>Created Tables (" . count($tables) . "):</h4>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>{$table}</li>";
        }
        echo "</ul>";
        
    } catch (Exception $e) {
        echo "<h3 style='color: red;'>✗ Database connection test failed: " . $e->getMessage() . "</h3>";
    }
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Database is ready for use</li>";
    echo "<li>You can now test the API endpoints</li>";
    echo "<li>Access the application at: <a href='../index.php'>../index.php</a></li>";
    echo "</ol>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Setup Failed: " . $e->getMessage() . "</h3>";
    echo "<p>Please check your database configuration in backend/config/database.php</p>";
}
?>

<?php include '../shared/header.html'; ?>

<div class="container my-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Home</a></li>
            <li class="breadcrumb-item active" aria-current="page">Products</li>
        </ol>
    </nav>
    
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h2">All Products</h1>
            <p class="text-muted" id="productCount">Loading products...</p>
        </div>
        <div class="col-md-6 text-md-end">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary active" id="gridView">
                    <i class="fas fa-th"></i> Grid
                </button>
                <button type="button" class="btn btn-outline-secondary" id="listView">
                    <i class="fas fa-list"></i> List
                </button>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Filters Sidebar -->
        <div class="col-lg-3 col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>Filters
                        <button class="btn btn-sm btn-outline-secondary float-end" id="clearFilters">Clear</button>
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Search -->
                    <div class="mb-3">
                        <label class="form-label">Search</label>
                        <input type="text" class="form-control" id="searchFilter" placeholder="Search products...">
                    </div>
                    
                    <!-- Categories -->
                    <div class="mb-3">
                        <label class="form-label">Category</label>
                        <select class="form-select" id="categoryFilter">
                            <option value="">All Categories</option>
                            <!-- Categories will be loaded here -->
                        </select>
                    </div>
                    
                    <!-- Price Range -->
                    <div class="mb-3">
                        <label class="form-label">Price Range (RWF)</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="number" class="form-control" id="minPrice" placeholder="Min">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control" id="maxPrice" placeholder="Max">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Vendors -->
                    <div class="mb-3">
                        <label class="form-label">Vendor</label>
                        <select class="form-select" id="vendorFilter">
                            <option value="">All Vendors</option>
                            <!-- Vendors will be loaded here -->
                        </select>
                    </div>
                    
                    <!-- Rating -->
                    <div class="mb-3">
                        <label class="form-label">Minimum Rating</label>
                        <select class="form-select" id="ratingFilter">
                            <option value="">Any Rating</option>
                            <option value="4">4+ Stars</option>
                            <option value="3">3+ Stars</option>
                            <option value="2">2+ Stars</option>
                            <option value="1">1+ Stars</option>
                        </select>
                    </div>
                    
                    <!-- Apply Filters Button -->
                    <button class="btn btn-primary w-100" id="applyFilters">
                        <i class="fas fa-search me-2"></i>Apply Filters
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Products Grid -->
        <div class="col-lg-9 col-md-8">
            <!-- Sort and View Options -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="d-flex align-items-center">
                    <label class="form-label me-2 mb-0">Sort by:</label>
                    <select class="form-select" id="sortBy" style="width: auto;">
                        <option value="created_at-desc">Newest First</option>
                        <option value="created_at-asc">Oldest First</option>
                        <option value="name-asc">Name A-Z</option>
                        <option value="name-desc">Name Z-A</option>
                        <option value="price-asc">Price Low to High</option>
                        <option value="price-desc">Price High to Low</option>
                    </select>
                </div>
                
                <div class="d-flex align-items-center">
                    <label class="form-label me-2 mb-0">Show:</label>
                    <select class="form-select" id="perPage" style="width: auto;">
                        <option value="12">12 per page</option>
                        <option value="24">24 per page</option>
                        <option value="48">48 per page</option>
                    </select>
                </div>
            </div>
            
            <!-- Products Container -->
            <div id="productsContainer">
                <!-- Loading spinner -->
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading products...</span>
                    </div>
                </div>
            </div>
            
            <!-- Pagination -->
            <nav aria-label="Products pagination" class="mt-4">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- Pagination will be generated here -->
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Product Card Template -->
<template id="productCardTemplate">
    <div class="col-lg-4 col-md-6 mb-4 product-card">
        <div class="card h-100 shadow-sm">
            <div class="position-relative">
                <img src="" class="card-img-top product-image" alt="" style="height: 200px; object-fit: cover;">
                <div class="position-absolute top-0 end-0 m-2">
                    <button class="btn btn-sm btn-outline-light rounded-circle wishlist-btn" title="Add to Wishlist">
                        <i class="far fa-heart"></i>
                    </button>
                </div>
                <div class="position-absolute bottom-0 start-0 m-2 product-badges">
                    <!-- Badges will be added here -->
                </div>
            </div>
            <div class="card-body d-flex flex-column">
                <h6 class="card-title product-name"></h6>
                <p class="card-text text-muted small product-description"></p>
                <div class="mt-auto">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div class="product-price">
                            <span class="h6 text-primary mb-0 current-price"></span>
                            <small class="text-muted text-decoration-line-through compare-price"></small>
                        </div>
                        <div class="product-rating">
                            <span class="rating-stars"></span>
                            <small class="text-muted rating-count"></small>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted vendor-name"></small>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary compare-btn" title="Compare">
                                <i class="fas fa-balance-scale"></i>
                            </button>
                            <button class="btn btn-primary add-to-cart-btn">
                                <i class="fas fa-cart-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
// Page-specific JavaScript
let currentPage = 1;
let currentFilters = {};
let currentSort = 'created_at-desc';
let currentPerPage = 12;
let isGridView = true;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize page
    loadCategories();
    loadVendors();
    loadProducts();
    
    // Event listeners
    document.getElementById('applyFilters').addEventListener('click', applyFilters);
    document.getElementById('clearFilters').addEventListener('click', clearFilters);
    document.getElementById('sortBy').addEventListener('change', handleSortChange);
    document.getElementById('perPage').addEventListener('change', handlePerPageChange);
    document.getElementById('gridView').addEventListener('click', () => setViewMode(true));
    document.getElementById('listView').addEventListener('click', () => setViewMode(false));
    
    // Search filter with debounce
    let searchTimeout;
    document.getElementById('searchFilter').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(applyFilters, 500);
    });
});

function loadCategories() {
    fetch('/api/categories')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('categoryFilter');
                data.data.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => console.error('Error loading categories:', error));
}

function loadVendors() {
    fetch('/api/vendors')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('vendorFilter');
                data.data.vendors.forEach(vendor => {
                    const option = document.createElement('option');
                    option.value = vendor.id;
                    option.textContent = vendor.business_name;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => console.error('Error loading vendors:', error));
}

function loadProducts() {
    showLoading();
    
    const params = new URLSearchParams({
        page: currentPage,
        limit: currentPerPage,
        sort: currentSort.split('-')[0],
        order: currentSort.split('-')[1],
        ...currentFilters
    });
    
    fetch(`/api/products?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayProducts(data.data.products);
                displayPagination(data.data.pagination);
                updateProductCount(data.data.pagination.total);
            } else {
                showError('Failed to load products');
            }
        })
        .catch(error => {
            console.error('Error loading products:', error);
            showError('Failed to load products');
        });
}

function displayProducts(products) {
    const container = document.getElementById('productsContainer');
    const template = document.getElementById('productCardTemplate');
    
    if (products.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4>No products found</h4>
                <p class="text-muted">Try adjusting your filters or search terms.</p>
            </div>
        `;
        return;
    }
    
    const containerClass = isGridView ? 'row' : 'list-group';
    container.innerHTML = `<div class="${containerClass}" id="productsList"></div>`;
    const productsList = document.getElementById('productsList');
    
    products.forEach(product => {
        const productElement = createProductCard(product, template);
        productsList.appendChild(productElement);
    });
}

function createProductCard(product, template) {
    const clone = template.content.cloneNode(true);
    
    // Set product data
    const img = clone.querySelector('.product-image');
    img.src = product.images && product.images.length > 0 ? product.images[0] : '/assets/images/no-image.png';
    img.alt = product.name;
    
    clone.querySelector('.product-name').textContent = product.name;
    clone.querySelector('.product-description').textContent = product.short_description || product.description.substring(0, 100) + '...';
    clone.querySelector('.current-price').textContent = formatCurrency(product.min_price);
    clone.querySelector('.vendor-name').textContent = product.vendor_name;
    
    // Rating
    const ratingStars = clone.querySelector('.rating-stars');
    const ratingCount = clone.querySelector('.rating-count');
    if (product.avg_rating > 0) {
        ratingStars.innerHTML = generateStars(product.avg_rating);
        ratingCount.textContent = `(${product.review_count})`;
    } else {
        ratingStars.innerHTML = '<small class="text-muted">No reviews</small>';
        ratingCount.textContent = '';
    }
    
    // Event listeners
    clone.querySelector('.card').addEventListener('click', () => {
        window.location.href = `/product?id=${product.id}`;
    });
    
    clone.querySelector('.compare-btn').addEventListener('click', (e) => {
        e.stopPropagation();
        addToCompare(product.id);
    });
    
    clone.querySelector('.add-to-cart-btn').addEventListener('click', (e) => {
        e.stopPropagation();
        addToCart(product.id);
    });
    
    clone.querySelector('.wishlist-btn').addEventListener('click', (e) => {
        e.stopPropagation();
        addToWishlist(product.id);
    });
    
    return clone;
}

function applyFilters() {
    currentFilters = {
        q: document.getElementById('searchFilter').value,
        category: document.getElementById('categoryFilter').value,
        vendor: document.getElementById('vendorFilter').value,
        min_price: document.getElementById('minPrice').value,
        max_price: document.getElementById('maxPrice').value,
        rating: document.getElementById('ratingFilter').value
    };
    
    // Remove empty filters
    Object.keys(currentFilters).forEach(key => {
        if (!currentFilters[key]) {
            delete currentFilters[key];
        }
    });
    
    currentPage = 1;
    loadProducts();
}

function clearFilters() {
    document.getElementById('searchFilter').value = '';
    document.getElementById('categoryFilter').value = '';
    document.getElementById('vendorFilter').value = '';
    document.getElementById('minPrice').value = '';
    document.getElementById('maxPrice').value = '';
    document.getElementById('ratingFilter').value = '';
    
    currentFilters = {};
    currentPage = 1;
    loadProducts();
}

function handleSortChange() {
    currentSort = document.getElementById('sortBy').value;
    currentPage = 1;
    loadProducts();
}

function handlePerPageChange() {
    currentPerPage = parseInt(document.getElementById('perPage').value);
    currentPage = 1;
    loadProducts();
}

function setViewMode(isGrid) {
    isGridView = isGrid;
    document.getElementById('gridView').classList.toggle('active', isGrid);
    document.getElementById('listView').classList.toggle('active', !isGrid);
    
    // Reload products with new view
    const container = document.getElementById('productsContainer');
    if (container.querySelector('#productsList')) {
        // Re-render current products
        loadProducts();
    }
}

function displayPagination(pagination) {
    const paginationContainer = document.getElementById('pagination');
    paginationContainer.innerHTML = '';
    
    if (pagination.total_pages <= 1) return;
    
    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${pagination.current_page === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" data-page="${pagination.current_page - 1}">Previous</a>`;
    paginationContainer.appendChild(prevLi);
    
    // Page numbers
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === pagination.current_page ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
        paginationContainer.appendChild(li);
    }
    
    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${pagination.current_page === pagination.total_pages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" data-page="${pagination.current_page + 1}">Next</a>`;
    paginationContainer.appendChild(nextLi);
    
    // Add click events
    paginationContainer.addEventListener('click', function(e) {
        e.preventDefault();
        if (e.target.classList.contains('page-link') && !e.target.parentElement.classList.contains('disabled')) {
            currentPage = parseInt(e.target.dataset.page);
            loadProducts();
        }
    });
}

function updateProductCount(total) {
    document.getElementById('productCount').textContent = `${total} products found`;
}

function showLoading() {
    document.getElementById('productsContainer').innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading products...</span>
            </div>
        </div>
    `;
}

function showError(message) {
    document.getElementById('productsContainer').innerHTML = `
        <div class="text-center py-5">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h4>Error</h4>
            <p class="text-muted">${message}</p>
            <button class="btn btn-primary" onclick="loadProducts()">Try Again</button>
        </div>
    `;
}

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('rw-RW', {
        style: 'currency',
        currency: 'RWF',
        minimumFractionDigits: 0
    }).format(amount);
}

function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let stars = '';
    
    for (let i = 0; i < fullStars; i++) {
        stars += '<i class="fas fa-star text-warning"></i>';
    }
    
    if (hasHalfStar) {
        stars += '<i class="fas fa-star-half-alt text-warning"></i>';
    }
    
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
        stars += '<i class="far fa-star text-warning"></i>';
    }
    
    return stars;
}

function addToCompare(productId) {
    // Implementation for compare functionality
    console.log('Add to compare:', productId);
    showToast('Product added to comparison', 'success');
}

function addToCart(productId) {
    // Implementation for cart functionality
    console.log('Add to cart:', productId);
    showToast('Product added to cart', 'success');
}

function addToWishlist(productId) {
    // Implementation for wishlist functionality
    console.log('Add to wishlist:', productId);
    showToast('Product added to wishlist', 'success');
}

function showToast(message, type = 'info') {
    // Simple toast implementation
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed top-0 end-0 m-3`;
    toast.style.zIndex = '9999';
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}
</script>

<?php include '../shared/footer.html'; ?>

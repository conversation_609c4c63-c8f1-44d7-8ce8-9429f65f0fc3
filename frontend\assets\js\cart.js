/**
 * Shopping Cart JavaScript
 * Musanze Marketplace
 */

class ShoppingCart {
    constructor() {
        this.items = storage.get(STORAGE_KEYS.cart, []);
        this.init();
    }
    
    init() {
        this.updateCartCount();
        this.bindEvents();
    }
    
    bindEvents() {
        // Listen for cart updates
        document.addEventListener(EVENTS.cartUpdate, () => {
            this.items = storage.get(STORAGE_KEYS.cart, []);
            this.updateCartCount();
        });
    }
    
    addItem(productId, variantId = null, quantity = 1) {
        const existingItemIndex = this.items.findIndex(item => 
            item.productId === productId && item.variantId === variantId
        );
        
        if (existingItemIndex > -1) {
            this.items[existingItemIndex].quantity += quantity;
        } else {
            this.items.push({
                id: generateId(),
                productId,
                variantId,
                quantity,
                addedAt: new Date().toISOString()
            });
        }
        
        this.saveCart();
        this.dispatchUpdate();
        return true;
    }
    
    removeItem(itemId) {
        this.items = this.items.filter(item => item.id !== itemId);
        this.saveCart();
        this.dispatchUpdate();
    }
    
    updateQuantity(itemId, quantity) {
        const item = this.items.find(item => item.id === itemId);
        if (item) {
            if (quantity <= 0) {
                this.removeItem(itemId);
            } else {
                item.quantity = quantity;
                this.saveCart();
                this.dispatchUpdate();
            }
        }
    }
    
    clearCart() {
        this.items = [];
        this.saveCart();
        this.dispatchUpdate();
    }
    
    getItems() {
        return this.items;
    }
    
    getItemCount() {
        return this.items.reduce((total, item) => total + item.quantity, 0);
    }
    
    getTotalPrice() {
        // This would need to fetch product prices from API
        // For now, return 0
        return 0;
    }
    
    saveCart() {
        storage.set(STORAGE_KEYS.cart, this.items);
    }
    
    dispatchUpdate() {
        document.dispatchEvent(new CustomEvent(EVENTS.cartUpdate, {
            detail: { items: this.items }
        }));
    }
    
    updateCartCount() {
        const count = this.getItemCount();
        const cartCountElement = document.getElementById('cartCount');
        if (cartCountElement) {
            cartCountElement.textContent = count;
            cartCountElement.style.display = count > 0 ? 'inline' : 'none';
        }
    }
}

// Create global cart instance
const cart = new ShoppingCart();

// Export for global use
window.cart = cart;

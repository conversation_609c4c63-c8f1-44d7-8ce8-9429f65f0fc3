<?php
/**
 * Product Model
 * Handles product data operations
 */

class Product {
    private $db;
    private $table = 'products';
    private $variantsTable = 'product_variants';
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Create a new product with variants
     */
    public function create($productData, $variants) {
        try {
            $this->db->beginTransaction();
            
            // Insert product
            $productId = $this->db->insert($this->table, $productData);
            
            // Insert variants
            foreach ($variants as $variant) {
                $variantData = [
                    'product_id' => $productId,
                    'name' => $variant['name'],
                    'sku' => $variant['sku'] ?? '',
                    'price' => $variant['price'],
                    'compare_price' => $variant['compare_price'] ?? null,
                    'cost_price' => $variant['cost_price'] ?? null,
                    'stock_quantity' => $variant['stock_quantity'] ?? 0,
                    'low_stock_threshold' => $variant['low_stock_threshold'] ?? 5,
                    'weight' => $variant['weight'] ?? null,
                    'dimensions' => json_encode($variant['dimensions'] ?? []),
                    'attributes' => json_encode($variant['attributes'] ?? []),
                    'images' => json_encode($variant['images'] ?? []),
                    'is_default' => $variant['is_default'] ?? false,
                    'status' => $variant['status'] ?? 'active'
                ];
                
                $this->db->insert($this->variantsTable, $variantData);
            }
            
            $this->db->commit();
            return $productId;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Find product by ID
     */
    public function findById($id) {
        $sql = "SELECT p.*, c.name as category_name, v.business_name as vendor_name,
                       u.name as vendor_contact_name, u.phone as vendor_phone
                FROM {$this->table} p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN vendors v ON p.vendor_id = v.id
                LEFT JOIN users u ON v.user_id = u.id
                WHERE p.id = :id";
        
        $product = $this->db->fetch($sql, ['id' => $id]);
        
        if ($product) {
            // Decode JSON fields
            $product['specifications'] = json_decode($product['specifications'] ?? '[]', true);
            $product['images'] = json_decode($product['images'] ?? '[]', true);
        }
        
        return $product;
    }
    
    /**
     * Get product variants
     */
    public function getVariants($productId) {
        $sql = "SELECT * FROM {$this->variantsTable} WHERE product_id = :product_id ORDER BY is_default DESC, price ASC";
        $variants = $this->db->fetchAll($sql, ['product_id' => $productId]);
        
        foreach ($variants as &$variant) {
            $variant['dimensions'] = json_decode($variant['dimensions'] ?? '[]', true);
            $variant['attributes'] = json_decode($variant['attributes'] ?? '[]', true);
            $variant['images'] = json_decode($variant['images'] ?? '[]', true);
        }
        
        return $variants;
    }
    
    /**
     * Get product reviews
     */
    public function getReviews($productId, $limit = 10) {
        $sql = "SELECT r.*, u.name as customer_name
                FROM reviews r
                LEFT JOIN users u ON r.customer_id = u.id
                WHERE r.product_id = :product_id AND r.status = 'approved'
                ORDER BY r.created_at DESC
                LIMIT :limit";
        
        return $this->db->fetchAll($sql, ['product_id' => $productId, 'limit' => $limit]);
    }
    
    /**
     * Find similar products for comparison
     */
    public function findSimilar($productName, $categoryId, $excludeId = null) {
        $whereClause = ["p.category_id = :category_id", "p.status = 'active'"];
        $params = ['category_id' => $categoryId];
        
        if ($excludeId) {
            $whereClause[] = "p.id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        // Search for similar names
        $whereClause[] = "(p.name LIKE :search1 OR p.name LIKE :search2)";
        $words = explode(' ', $productName);
        $params['search1'] = '%' . $words[0] . '%';
        $params['search2'] = '%' . end($words) . '%';
        
        $whereSQL = 'WHERE ' . implode(' AND ', $whereClause);
        
        $sql = "SELECT p.*, v.business_name as vendor_name,
                       (SELECT MIN(price) FROM {$this->variantsTable} WHERE product_id = p.id AND status = 'active') as min_price,
                       (SELECT AVG(rating) FROM reviews WHERE product_id = p.id AND status = 'approved') as avg_rating
                FROM {$this->table} p
                LEFT JOIN vendors v ON p.vendor_id = v.id
                {$whereSQL}
                ORDER BY p.created_at DESC
                LIMIT 10";
        
        $products = $this->db->fetchAll($sql, $params);
        
        foreach ($products as &$product) {
            $product['images'] = json_decode($product['images'] ?? '[]', true);
            $product['avg_rating'] = $product['avg_rating'] ? round($product['avg_rating'], 1) : 0;
        }
        
        return $products;
    }
    
    /**
     * Update product
     */
    public function update($id, $data) {
        return $this->db->update($this->table, $data, 'id = :id', ['id' => $id]);
    }
    
    /**
     * Update product variants
     */
    public function updateVariants($productId, $variants) {
        try {
            $this->db->beginTransaction();
            
            // Delete existing variants
            $this->db->delete($this->variantsTable, 'product_id = :product_id', ['product_id' => $productId]);
            
            // Insert new variants
            foreach ($variants as $variant) {
                $variantData = [
                    'product_id' => $productId,
                    'name' => $variant['name'],
                    'sku' => $variant['sku'] ?? '',
                    'price' => $variant['price'],
                    'compare_price' => $variant['compare_price'] ?? null,
                    'cost_price' => $variant['cost_price'] ?? null,
                    'stock_quantity' => $variant['stock_quantity'] ?? 0,
                    'low_stock_threshold' => $variant['low_stock_threshold'] ?? 5,
                    'weight' => $variant['weight'] ?? null,
                    'dimensions' => json_encode($variant['dimensions'] ?? []),
                    'attributes' => json_encode($variant['attributes'] ?? []),
                    'images' => json_encode($variant['images'] ?? []),
                    'is_default' => $variant['is_default'] ?? false,
                    'status' => $variant['status'] ?? 'active'
                ];
                
                $this->db->insert($this->variantsTable, $variantData);
            }
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Delete product
     */
    public function delete($id) {
        try {
            $this->db->beginTransaction();
            
            // Delete variants first
            $this->db->delete($this->variantsTable, 'product_id = :product_id', ['product_id' => $id]);
            
            // Delete product
            $this->db->delete($this->table, 'id = :id', ['id' => $id]);
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Get featured products
     */
    public function getFeatured($limit = 8) {
        $sql = "SELECT p.*, v.business_name as vendor_name,
                       (SELECT MIN(price) FROM {$this->variantsTable} WHERE product_id = p.id AND status = 'active') as min_price,
                       (SELECT AVG(rating) FROM reviews WHERE product_id = p.id AND status = 'approved') as avg_rating
                FROM {$this->table} p
                LEFT JOIN vendors v ON p.vendor_id = v.id
                WHERE p.featured = 1 AND p.status = 'active'
                ORDER BY p.created_at DESC
                LIMIT :limit";
        
        $products = $this->db->fetchAll($sql, ['limit' => $limit]);
        
        foreach ($products as &$product) {
            $product['images'] = json_decode($product['images'] ?? '[]', true);
            $product['avg_rating'] = $product['avg_rating'] ? round($product['avg_rating'], 1) : 0;
        }
        
        return $products;
    }
    
    /**
     * Search products
     */
    public function search($query, $limit = 20) {
        $sql = "SELECT p.*, v.business_name as vendor_name,
                       (SELECT MIN(price) FROM {$this->variantsTable} WHERE product_id = p.id AND status = 'active') as min_price
                FROM {$this->table} p
                LEFT JOIN vendors v ON p.vendor_id = v.id
                WHERE p.status = 'active' AND (
                    p.name LIKE :query OR 
                    p.description LIKE :query OR 
                    p.brand LIKE :query OR
                    MATCH(p.name, p.description) AGAINST(:query_ft IN NATURAL LANGUAGE MODE)
                )
                ORDER BY 
                    CASE WHEN p.name LIKE :exact_query THEN 1 ELSE 2 END,
                    p.featured DESC,
                    p.created_at DESC
                LIMIT :limit";
        
        $searchQuery = '%' . $query . '%';
        $exactQuery = '%' . $query . '%';
        
        return $this->db->fetchAll($sql, [
            'query' => $searchQuery,
            'query_ft' => $query,
            'exact_query' => $exactQuery,
            'limit' => $limit
        ]);
    }
}

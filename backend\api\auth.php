<?php
/**
 * Authentication API Endpoints
 */

require_once '../models/User.php';

// Get remaining URI parts
$action = isset($uri_parts[1]) ? $uri_parts[1] : '';

switch ($method) {
    case 'POST':
        switch ($action) {
            case 'register':
                handleRegister();
                break;
                
            case 'login':
                handleLogin();
                break;
                
            case 'logout':
                handleLogout();
                break;
                
            case 'refresh':
                handleRefresh();
                break;
                
            default:
                sendError('Invalid auth action', 400);
        }
        break;
        
    case 'GET':
        switch ($action) {
            case 'me':
                handleGetCurrentUser();
                break;
                
            default:
                sendError('Invalid auth action', 400);
        }
        break;
        
    default:
        sendError('Method not allowed', 405);
}

function handleRegister() {
    global $input;
    
    // Validate input
    $required = ['name', 'email', 'password', 'phone'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            sendError("Field '{$field}' is required", 400);
        }
    }
    
    // Validate email
    if (!is_valid_email($input['email'])) {
        sendError('Invalid email format', 400);
    }
    
    // Validate phone
    if (!is_valid_phone($input['phone'])) {
        sendError('Invalid phone number format', 400);
    }
    
    // Validate password
    if (strlen($input['password']) < 6) {
        sendError('Password must be at least 6 characters', 400);
    }
    
    try {
        $userModel = new User();
        
        // Check if email exists
        if ($userModel->findByEmail($input['email'])) {
            sendError('Email already exists', 409);
        }
        
        // Check if phone exists
        if ($userModel->findByPhone($input['phone'])) {
            sendError('Phone number already exists', 409);
        }
        
        // Create user
        $userData = [
            'name' => sanitize($input['name']),
            'email' => sanitize($input['email']),
            'phone' => sanitize($input['phone']),
            'password' => password_hash($input['password'], PASSWORD_DEFAULT),
            'role' => isset($input['role']) && in_array($input['role'], ['customer', 'vendor']) ? $input['role'] : 'customer'
        ];
        
        $userId = $userModel->create($userData);
        
        // Get created user
        $user = $userModel->findById($userId);
        unset($user['password']); // Remove password from response
        
        // Create session
        $_SESSION['user_id'] = $userId;
        $_SESSION['user_role'] = $user['role'];
        
        sendResponse([
            'user' => $user,
            'message' => 'Registration successful'
        ], 201);
        
    } catch (Exception $e) {
        error_log('Registration error: ' . $e->getMessage());
        sendError('Registration failed', 500);
    }
}

function handleLogin() {
    global $input;
    
    // Validate input
    if (empty($input['email']) || empty($input['password'])) {
        sendError('Email and password are required', 400);
    }
    
    try {
        $userModel = new User();
        $user = $userModel->findByEmail($input['email']);
        
        if (!$user || !password_verify($input['password'], $user['password'])) {
            sendError('Invalid credentials', 401);
        }
        
        if ($user['status'] !== 'active') {
            sendError('Account is not active', 403);
        }
        
        // Create session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_role'] = $user['role'];
        
        // Update last login
        $userModel->updateLastLogin($user['id']);
        
        // Remove password from response
        unset($user['password']);
        
        sendResponse([
            'user' => $user,
            'message' => 'Login successful'
        ]);
        
    } catch (Exception $e) {
        error_log('Login error: ' . $e->getMessage());
        sendError('Login failed', 500);
    }
}

function handleLogout() {
    session_destroy();
    sendResponse(['message' => 'Logout successful']);
}

function handleRefresh() {
    if (!isset($_SESSION['user_id'])) {
        sendError('Not authenticated', 401);
    }
    
    try {
        $userModel = new User();
        $user = $userModel->findById($_SESSION['user_id']);
        
        if (!$user || $user['status'] !== 'active') {
            session_destroy();
            sendError('Session invalid', 401);
        }
        
        unset($user['password']);
        sendResponse(['user' => $user]);
        
    } catch (Exception $e) {
        error_log('Refresh error: ' . $e->getMessage());
        sendError('Session refresh failed', 500);
    }
}

function handleGetCurrentUser() {
    if (!isset($_SESSION['user_id'])) {
        sendError('Not authenticated', 401);
    }
    
    try {
        $userModel = new User();
        $user = $userModel->findById($_SESSION['user_id']);
        
        if (!$user) {
            session_destroy();
            sendError('User not found', 404);
        }
        
        unset($user['password']);
        sendResponse(['user' => $user]);
        
    } catch (Exception $e) {
        error_log('Get user error: ' . $e->getMessage());
        sendError('Failed to get user', 500);
    }
}
?>

/**
 * Main Stylesheet
 * Musanze District Product Price Comparison Platform
 */

/* CSS Custom Properties (Variables) */
:root {
    /* Primary Colors */
    --primary-blue: #1E40AF;
    --success-green: #059669;
    --warning-orange: #D97706;
    --error-red: #DC2626;
    
    /* Secondary Colors */
    --light-gray: #F3F4F6;
    --medium-gray: #6B7280;
    --dark-gray: #1F2937;
    --white: #FFFFFF;
    
    /* Rwanda Flag Colors */
    --rwanda-blue: #00A1DE;
    --rwanda-yellow: #FFDA00;
    --rwanda-green: #00A651;
    
    /* Typography */
    --font-primary: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-secondary: 'Roboto', Arial, sans-serif;
    --font-monospace: 'Fira Code', 'Courier New', monospace;
    
    /* Font Sizes */
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 2rem;      /* 32px */
    --font-size-4xl: 2.5rem;    /* 40px */
    
    /* Spacing */
    --spacing-xs: 0.25rem;   /* 4px */
    --spacing-sm: 0.5rem;    /* 8px */
    --spacing-md: 1rem;      /* 16px */
    --spacing-lg: 1.5rem;    /* 24px */
    --spacing-xl: 2rem;      /* 32px */
    --spacing-2xl: 3rem;     /* 48px */
    
    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--dark-gray);
    background-color: var(--light-gray);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-md);
}

a {
    color: var(--primary-blue);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--rwanda-blue);
}

/* Custom Bootstrap Overrides */
.btn-primary {
    background-color: var(--primary-blue);
    border-color: var(--primary-blue);
}

.btn-primary:hover {
    background-color: var(--rwanda-blue);
    border-color: var(--rwanda-blue);
}

.btn-success {
    background-color: var(--success-green);
    border-color: var(--success-green);
}

.btn-warning {
    background-color: var(--warning-orange);
    border-color: var(--warning-orange);
}

.btn-danger {
    background-color: var(--error-red);
    border-color: var(--error-red);
}

.text-primary {
    color: var(--primary-blue) !important;
}

.text-success {
    color: var(--success-green) !important;
}

.text-warning {
    color: var(--warning-orange) !important;
}

.text-danger {
    color: var(--error-red) !important;
}

.bg-primary {
    background-color: var(--primary-blue) !important;
}

/* Navigation Styles */
.navbar-brand {
    font-weight: 700;
    font-size: var(--font-size-xl);
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    transition: color var(--transition-fast);
}

.navbar-nav .nav-link:hover {
    color: var(--primary-blue);
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
    background-color: var(--white);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Back to Top Button */
#backToTop {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
}

#backToTop:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Utility Classes */
.text-muted {
    color: var(--medium-gray) !important;
}

.bg-light-gray {
    background-color: var(--light-gray) !important;
}

.border-radius-sm {
    border-radius: var(--border-radius-sm) !important;
}

.border-radius-md {
    border-radius: var(--border-radius-md) !important;
}

.border-radius-lg {
    border-radius: var(--border-radius-lg) !important;
}

.shadow-custom {
    box-shadow: var(--shadow-md);
}

.transition-fast {
    transition: all var(--transition-fast);
}

.transition-normal {
    transition: all var(--transition-normal);
}

/* Responsive Utilities */
@media (max-width: 575.98px) {
    h1 { font-size: var(--font-size-3xl); }
    h2 { font-size: var(--font-size-2xl); }
    h3 { font-size: var(--font-size-xl); }
    
    .container {
        padding-left: var(--spacing-md);
        padding-right: var(--spacing-md);
    }
}

/* Print Styles */
@media print {
    .navbar,
    .footer,
    #backToTop,
    .loading-overlay,
    .toast-container {
        display: none !important;
    }
    
    body {
        background-color: white !important;
        color: black !important;
    }
    
    a {
        color: black !important;
        text-decoration: underline !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-blue: #000080;
        --success-green: #006400;
        --warning-orange: #FF8C00;
        --error-red: #8B0000;
        --medium-gray: #404040;
        --dark-gray: #000000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus Styles for Accessibility */
*:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

button:focus,
.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(30, 64, 175, 0.25);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--medium-gray);
    border-radius: var(--border-radius-md);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-gray);
}

/**
 * Configuration JavaScript
 * Musanze Marketplace
 */

// Application configuration
const APP_CONFIG = {
    name: 'Musanze Marketplace',
    version: '1.0.0',
    api: {
        baseUrl: '/api',
        timeout: 30000
    },
    currency: {
        code: 'RWF',
        symbol: 'RWF',
        locale: 'rw-RW'
    },
    pagination: {
        defaultPageSize: 20,
        maxPageSize: 100
    },
    features: {
        userRegistration: true,
        vendorRegistration: true,
        emailVerification: false,
        smsVerification: false,
        priceAlerts: true,
        reviewsEnabled: true,
        wishlistEnabled: true,
        cartEnabled: true,
        multiLanguage: true,
        analyticsEnabled: true
    },
    languages: {
        en: 'English',
        rw: 'Kinyarwanda',
        fr: 'Français'
    },
    defaultLanguage: 'en',
    userRoles: {
        customer: 'Customer',
        vendor: 'Vendor',
        admin: 'Administrator'
    },
    orderStatuses: {
        pending: 'Pending',
        confirmed: 'Confirmed',
        processing: 'Processing',
        shipped: 'Shipped',
        delivered: 'Delivered',
        cancelled: 'Cancelled',
        refunded: 'Refunded'
    },
    paymentStatuses: {
        pending: 'Pending',
        paid: 'Paid',
        failed: 'Failed',
        refunded: 'Refunded'
    },
    productStatuses: {
        draft: 'Draft',
        active: 'Active',
        inactive: 'Inactive',
        out_of_stock: 'Out of Stock'
    },
    vendorStatuses: {
        pending: 'Pending Approval',
        approved: 'Approved',
        rejected: 'Rejected',
        suspended: 'Suspended'
    }
};

// Theme configuration
const THEME_CONFIG = {
    colors: {
        primary: '#1E40AF',
        success: '#059669',
        warning: '#D97706',
        danger: '#DC2626',
        info: '#0EA5E9',
        light: '#F3F4F6',
        dark: '#1F2937',
        rwandaBlue: '#00A1DE',
        rwandaYellow: '#FFDA00',
        rwandaGreen: '#00A651'
    },
    breakpoints: {
        xs: 0,
        sm: 576,
        md: 768,
        lg: 992,
        xl: 1200,
        xxl: 1400
    }
};

// Validation rules
const VALIDATION_RULES = {
    email: {
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: 'Please enter a valid email address'
    },
    phone: {
        pattern: /^(\+250|0)[7][0-9]{8}$/,
        message: 'Please enter a valid Rwanda phone number (+250XXXXXXXXX or 07XXXXXXXX)'
    },
    password: {
        minLength: 6,
        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,
        message: 'Password must be at least 6 characters with uppercase, lowercase, and number'
    },
    required: {
        message: 'This field is required'
    }
};

// Local storage keys
const STORAGE_KEYS = {
    user: 'musanze_user',
    cart: 'musanze_cart',
    wishlist: 'musanze_wishlist',
    compare: 'musanze_compare',
    language: 'musanze_language',
    theme: 'musanze_theme',
    recentSearches: 'musanze_recent_searches'
};

// Event names
const EVENTS = {
    userLogin: 'user:login',
    userLogout: 'user:logout',
    cartUpdate: 'cart:update',
    wishlistUpdate: 'wishlist:update',
    compareUpdate: 'compare:update',
    languageChange: 'language:change',
    themeChange: 'theme:change'
};

// Error messages
const ERROR_MESSAGES = {
    network: 'Network error. Please check your connection and try again.',
    server: 'Server error. Please try again later.',
    unauthorized: 'You need to log in to perform this action.',
    forbidden: 'You do not have permission to perform this action.',
    notFound: 'The requested resource was not found.',
    validation: 'Please check your input and try again.',
    unknown: 'An unexpected error occurred. Please try again.'
};

// Success messages
const SUCCESS_MESSAGES = {
    login: 'Login successful!',
    logout: 'Logout successful!',
    register: 'Registration successful!',
    productAdded: 'Product added successfully!',
    productUpdated: 'Product updated successfully!',
    productDeleted: 'Product deleted successfully!',
    cartAdded: 'Product added to cart!',
    wishlistAdded: 'Product added to wishlist!',
    compareAdded: 'Product added to comparison!',
    orderPlaced: 'Order placed successfully!',
    profileUpdated: 'Profile updated successfully!'
};

// Export configuration for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        APP_CONFIG,
        THEME_CONFIG,
        VALIDATION_RULES,
        STORAGE_KEYS,
        EVENTS,
        ERROR_MESSAGES,
        SUCCESS_MESSAGES
    };
}

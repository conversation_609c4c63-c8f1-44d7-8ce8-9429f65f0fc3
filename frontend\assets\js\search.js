/**
 * Search JavaScript
 * Musanze Marketplace
 */

class Search {
    constructor() {
        this.recentSearches = storage.get(STORAGE_KEYS.recentSearches, []);
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.setupAutocomplete();
    }
    
    bindEvents() {
        // Search form submissions
        const searchForms = document.querySelectorAll('.search-form');
        searchForms.forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                const input = form.querySelector('input[type="search"], input[name="q"]');
                if (input) {
                    this.performSearch(input.value);
                }
            });
        });
        
        // Search input key events
        const searchInputs = document.querySelectorAll('#searchInput, #mobileSearchInput, #heroSearchInput');
        searchInputs.forEach(input => {
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch(input.value);
                }
            });
            
            // Setup autocomplete
            input.addEventListener('input', debounce((e) => {
                this.showSuggestions(e.target);
            }, 300));
            
            input.addEventListener('focus', (e) => {
                this.showRecentSearches(e.target);
            });
            
            input.addEventListener('blur', (e) => {
                // Hide suggestions after a delay to allow clicking
                setTimeout(() => {
                    this.hideSuggestions(e.target);
                }, 200);
            });
        });
        
        // Search buttons
        const searchButtons = document.querySelectorAll('#searchBtn, #mobileSearchBtn, #heroSearchBtn');
        searchButtons.forEach(button => {
            button.addEventListener('click', () => {
                const input = this.findSearchInput(button);
                if (input) {
                    this.performSearch(input.value);
                }
            });
        });
    }
    
    findSearchInput(button) {
        return button.previousElementSibling || 
               button.parentElement.querySelector('input') ||
               document.querySelector('#searchInput, #mobileSearchInput, #heroSearchInput');
    }
    
    performSearch(query) {
        if (!query.trim()) return;
        
        // Save to recent searches
        this.saveRecentSearch(query);
        
        // Hide suggestions
        this.hideAllSuggestions();
        
        // Redirect to products page with search query
        window.location.href = `/products?q=${encodeURIComponent(query.trim())}`;
    }
    
    saveRecentSearch(query) {
        query = query.trim();
        
        // Remove if already exists
        const index = this.recentSearches.indexOf(query);
        if (index > -1) {
            this.recentSearches.splice(index, 1);
        }
        
        // Add to beginning
        this.recentSearches.unshift(query);
        
        // Keep only last 10 searches
        if (this.recentSearches.length > 10) {
            this.recentSearches.splice(10);
        }
        
        storage.set(STORAGE_KEYS.recentSearches, this.recentSearches);
    }
    
    setupAutocomplete() {
        // Create suggestion containers for each search input
        const searchInputs = document.querySelectorAll('#searchInput, #mobileSearchInput, #heroSearchInput');
        searchInputs.forEach(input => {
            const container = document.createElement('div');
            container.className = 'search-suggestions position-absolute bg-white border rounded shadow-sm';
            container.style.cssText = `
                top: 100%;
                left: 0;
                right: 0;
                z-index: 1000;
                max-height: 300px;
                overflow-y: auto;
                display: none;
            `;
            
            // Insert after the input's parent
            const parent = input.parentElement;
            parent.style.position = 'relative';
            parent.appendChild(container);
            
            input.suggestionContainer = container;
        });
    }
    
    async showSuggestions(input) {
        const query = input.value.trim();
        if (query.length < 2) {
            this.hideSuggestions(input);
            return;
        }
        
        try {
            // Get product suggestions from API
            const response = await api.get('products', {
                q: query,
                limit: 5
            });
            
            if (response.success && response.data.products.length > 0) {
                this.renderSuggestions(input, response.data.products, 'products');
            } else {
                this.hideSuggestions(input);
            }
        } catch (error) {
            console.error('Error fetching suggestions:', error);
            this.hideSuggestions(input);
        }
    }
    
    showRecentSearches(input) {
        if (input.value.trim() || this.recentSearches.length === 0) return;
        
        this.renderSuggestions(input, this.recentSearches, 'recent');
    }
    
    renderSuggestions(input, items, type) {
        const container = input.suggestionContainer;
        if (!container) return;
        
        let html = '';
        
        if (type === 'products') {
            html = items.map(product => `
                <div class="suggestion-item p-2 border-bottom cursor-pointer" data-query="${product.name}">
                    <div class="d-flex align-items-center">
                        <img src="${product.images && product.images[0] ? product.images[0] : '/assets/images/no-image.png'}" 
                             alt="${product.name}" class="me-2" style="width: 40px; height: 40px; object-fit: cover;">
                        <div class="flex-grow-1">
                            <div class="fw-medium">${product.name}</div>
                            <small class="text-muted">${formatCurrency(product.min_price)}</small>
                        </div>
                        <i class="fas fa-search text-muted"></i>
                    </div>
                </div>
            `).join('');
        } else if (type === 'recent') {
            html = `
                <div class="p-2 border-bottom bg-light">
                    <small class="text-muted">Recent Searches</small>
                </div>
            ` + items.map(query => `
                <div class="suggestion-item p-2 border-bottom cursor-pointer" data-query="${query}">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-history text-muted me-2"></i>
                        <span class="flex-grow-1">${query}</span>
                        <i class="fas fa-search text-muted"></i>
                    </div>
                </div>
            `).join('');
        }
        
        container.innerHTML = html;
        container.style.display = 'block';
        
        // Add click events to suggestions
        container.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                const query = item.dataset.query;
                input.value = query;
                this.performSearch(query);
            });
        });
    }
    
    hideSuggestions(input) {
        if (input.suggestionContainer) {
            input.suggestionContainer.style.display = 'none';
        }
    }
    
    hideAllSuggestions() {
        const containers = document.querySelectorAll('.search-suggestions');
        containers.forEach(container => {
            container.style.display = 'none';
        });
    }
    
    clearRecentSearches() {
        this.recentSearches = [];
        storage.remove(STORAGE_KEYS.recentSearches);
    }
}

// Create global search instance
const search = new Search();

// Export for global use
window.search = search;

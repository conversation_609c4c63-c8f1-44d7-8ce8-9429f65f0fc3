<?php
/**
 * Application Configuration
 * Musanze District Product Price Comparison Platform
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Error reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Application settings
define('APP_NAME', 'Musanze Marketplace');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/ange Final');
define('APP_TIMEZONE', 'Africa/Kigali');

// Set timezone
date_default_timezone_set(APP_TIMEZONE);

// Directory paths
define('ROOT_PATH', dirname(dirname(__DIR__)));
define('BACKEND_PATH', ROOT_PATH . '/backend');
define('FRONTEND_PATH', ROOT_PATH . '/frontend');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('LOGS_PATH', ROOT_PATH . '/logs');

// URL paths
define('BASE_URL', APP_URL);
define('API_URL', BASE_URL . '/backend/api');
define('ASSETS_URL', BASE_URL . '/frontend/assets');
define('UPLOADS_URL', BASE_URL . '/uploads');

// Database settings (imported from database.php)
require_once BACKEND_PATH . '/config/database.php';

// Security settings
define('JWT_SECRET', 'your-secret-key-change-in-production');
define('PASSWORD_SALT', 'your-password-salt-change-in-production');
define('SESSION_LIFETIME', 3600); // 1 hour

// File upload settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx']);

// Pagination settings
define('DEFAULT_PAGE_SIZE', 20);
define('MAX_PAGE_SIZE', 100);

// Currency settings
define('DEFAULT_CURRENCY', 'RWF');
define('CURRENCY_SYMBOL', 'RWF');

// Email settings (configure for production)
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Musanze Marketplace');

// SMS settings (configure for production)
define('SMS_PROVIDER', 'local'); // local, twilio, etc.
define('SMS_API_KEY', '');
define('SMS_API_SECRET', '');

// Cache settings
define('CACHE_ENABLED', false);
define('CACHE_LIFETIME', 3600); // 1 hour

// API settings
define('API_VERSION', 'v1');
define('API_RATE_LIMIT', 100); // requests per minute
define('API_RATE_LIMIT_WINDOW', 60); // seconds

// Application features
define('FEATURES', [
    'user_registration' => true,
    'vendor_registration' => true,
    'email_verification' => false,
    'sms_verification' => false,
    'price_alerts' => true,
    'reviews_enabled' => true,
    'wishlist_enabled' => true,
    'cart_enabled' => true,
    'multi_language' => true,
    'analytics_enabled' => true
]);

// Supported languages
define('SUPPORTED_LANGUAGES', [
    'en' => 'English',
    'rw' => 'Kinyarwanda',
    'fr' => 'Français'
]);

// Default language
define('DEFAULT_LANGUAGE', 'en');

// User roles
define('USER_ROLES', [
    'customer' => 'Customer',
    'vendor' => 'Vendor',
    'admin' => 'Administrator'
]);

// Order statuses
define('ORDER_STATUSES', [
    'pending' => 'Pending',
    'confirmed' => 'Confirmed',
    'processing' => 'Processing',
    'shipped' => 'Shipped',
    'delivered' => 'Delivered',
    'cancelled' => 'Cancelled',
    'refunded' => 'Refunded'
]);

// Payment statuses
define('PAYMENT_STATUSES', [
    'pending' => 'Pending',
    'paid' => 'Paid',
    'failed' => 'Failed',
    'refunded' => 'Refunded'
]);

// Product statuses
define('PRODUCT_STATUSES', [
    'draft' => 'Draft',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'out_of_stock' => 'Out of Stock'
]);

// Vendor statuses
define('VENDOR_STATUSES', [
    'pending' => 'Pending Approval',
    'approved' => 'Approved',
    'rejected' => 'Rejected',
    'suspended' => 'Suspended'
]);

/**
 * Utility functions
 */

/**
 * Get configuration value
 */
function config($key, $default = null) {
    return defined($key) ? constant($key) : $default;
}

/**
 * Generate URL
 */
function url($path = '') {
    return BASE_URL . '/' . ltrim($path, '/');
}

/**
 * Generate API URL
 */
function api_url($endpoint = '') {
    return API_URL . '/' . ltrim($endpoint, '/');
}

/**
 * Generate asset URL
 */
function asset($path) {
    return ASSETS_URL . '/' . ltrim($path, '/');
}

/**
 * Generate upload URL
 */
function upload_url($path) {
    return UPLOADS_URL . '/' . ltrim($path, '/');
}

/**
 * Sanitize input
 */
function sanitize($input) {
    if (is_array($input)) {
        return array_map('sanitize', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email
 */
function is_valid_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate phone number (Rwanda format)
 */
function is_valid_phone($phone) {
    // Rwanda phone number format: +250XXXXXXXXX or 07XXXXXXXX
    $pattern = '/^(\+250|0)[7][0-9]{8}$/';
    return preg_match($pattern, $phone);
}

/**
 * Format currency
 */
function format_currency($amount, $currency = DEFAULT_CURRENCY) {
    return number_format($amount, 0, '.', ',') . ' ' . $currency;
}

/**
 * Generate random string
 */
function generate_random_string($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
}

/**
 * Log message
 */
function log_message($message, $level = 'info') {
    $log_file = LOGS_PATH . '/' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    
    // Create logs directory if it doesn't exist
    if (!is_dir(LOGS_PATH)) {
        mkdir(LOGS_PATH, 0755, true);
    }
    
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

// Create necessary directories
$directories = [UPLOADS_PATH, LOGS_PATH];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Include autoloader if using Composer
if (file_exists(ROOT_PATH . '/vendor/autoload.php')) {
    require_once ROOT_PATH . '/vendor/autoload.php';
}
?>
